import './instrument'; // Import this first for Sentry instrumentation
import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { Logger, ValidationPipe, BadRequestException } from '@nestjs/common';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import {
  utilities as nestWinstonModuleUtilities,
  WinstonModule,
} from 'nest-winston';
// DateTime import removed as we're using native Date
import { ConfigService } from '@nestjs/config';
// import { StreamingWsService } from './streaming-ws/streaming-ws.service'; // Commented out as its initialization is also commented out below
import { WsAdapter } from '@nestjs/platform-ws'; // <-- Import WsAdapter
import * as Sentry from '@sentry/nestjs';

async function bootstrap() {
  const isColorEnabled = process.env.APP_LOG_COLOR === 'true';
  const logLevel = process.env.APP_LOG_LEVEL || 'info';
  const instance = winston.createLogger({
    level: logLevel,
    format: winston.format.json(),
    transports: [
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.timestamp({
            format: (): string => {
              return new Date().toISOString();
            },
          }),
          winston.format.ms(),
          nestWinstonModuleUtilities.format.nestLike('Middleware API', {
            colors: isColorEnabled,
            prettyPrint: true,
          }),
        ),
      }),
      new DailyRotateFile({
        filename: 'logs/middleware-api-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '14d',
        format: winston.format.combine(
          winston.format.timestamp({
            format: (): string => {
              return new Date().toISOString();
            },
          }),
          winston.format.printf((info) => {
            const safeTimestamp =
              typeof info.timestamp === 'string'
                ? info.timestamp
                : new Date().toISOString();
            const safeLevel =
              typeof info.level === 'string' ? info.level : 'info';
            // Handle message safely to avoid stringification issues
            let safeMessage = '';
            if (typeof info.message === 'string') {
              safeMessage = info.message;
            } else if (info.message) {
              try {
                safeMessage = JSON.stringify(info.message);
              } catch {
                safeMessage = '[Complex object]';
              }
            }
            return `${safeTimestamp} [${safeLevel}]: ${safeMessage}`;
          }),
        ),
      }),
    ],
  });

  try {
    const app = await NestFactory.create(AppModule, {
      logger: WinstonModule.createLogger({
        instance,
      }),
      cors: true,
    });

    // Explicitly use the WsAdapter for WebSocket connections
    app.useWebSocketAdapter(new WsAdapter(app));

    // Set global prefix for all routes
    app.setGlobalPrefix('api/v1');

    // Get the WebSocket services and initialize them
    /*     const streamingWsService = app.get(StreamingWsService);
    streamingWsService.initialize(app); */

    // Enable validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true, // Strips properties not in the DTO
        forbidNonWhitelisted: true, // Throws error if non-whitelisted properties are present
        transform: true, // Automatically transforms payload to DTO instance
        transformOptions: {
          enableImplicitConversion: true, // Allows basic type coercion
        },
        // Add exceptionFactory for detailed logging
        exceptionFactory: (errors) => {
          const messages = errors.map(
            (error) =>
              `${error.property} has failed constraints: ${Object.values(error.constraints ?? {}).join(', ')}`,
          );
          // Log the detailed validation errors using the winston instance
          instance.error(
            `Validation failed: ${JSON.stringify(messages)}`,
            { context: 'ValidationPipe' }, // Add context for clarity
          );
          // Return the standard BadRequestException
          return new BadRequestException(messages);
        },
      }),
    );

    // Enable CORS
    const allowedOrigins: string | string[] = process.env.CORS_ALLOWED_ORIGINS
      ? process.env.CORS_ALLOWED_ORIGINS.includes(',')
        ? process.env.CORS_ALLOWED_ORIGINS.split(',').map((origin) =>
            origin.trim(),
          )
        : process.env.CORS_ALLOWED_ORIGINS
      : '*';

    app.enableCors({
      origin: allowedOrigins,
      methods: process.env.CORS_ALLOWED_METHODS?.split(',') || [
        'GET',
        'POST',
        'PUT',
        'DELETE',
        'OPTIONS',
      ],
      allowedHeaders: process.env.CORS_ALLOWED_HEADERS?.split(',') || ['*'],
      credentials: process.env.CORS_CREDENTIALS === 'true',
      maxAge: parseInt(process.env.CORS_MAX_AGE || '3600', 10),
    });

    const config = new DocumentBuilder()
      .setTitle('Middleware API')
      .setDescription('API documentation for Middleware service')
      .setVersion('1.0')
      .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: 'Enter JWT token',
        in: 'header',
      })
      .build();

    const document = SwaggerModule.createDocument(app, config);

    // Customize Swagger setup
    SwaggerModule.setup('api/docs', app, document, {
      yamlDocumentUrl: 'api/docs/yaml',
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
        docExpansion: 'none',
      },
    });

    const configService = app.get(ConfigService);
    const port = configService.get<number>('PORT') || 3000;

    await app.listen(port, '0.0.0.0');

    const serverUrl = await app.getUrl();

    const logger = new Logger(AppModule.name);
    logger.log(`Server running on ${serverUrl}`);
    logger.log(`Swagger documentation available at ${serverUrl}/api/docs`);
    /*     logger.log(`WebSocket endpoints:`);
    logger.log(` - Streaming WebSocket: ${serverUrl}/api/v1/streaming-ws`); */

    // Improved shutdown handling
    const shutdownGracefully = async (signal: string) => {
      instance.info(`${signal} received, starting graceful shutdown...`);

      try {
        // Set a timeout to force exit if shutdown takes too long
        const forceExitTimeout = setTimeout((): void => {
          instance.error('Forced exit after shutdown timeout');
          process.exit(1);
        }, 10000); // 10 seconds timeout

        await app.close();

        clearTimeout(forceExitTimeout);

        instance.info('Graceful shutdown completed successfully');
        process.exit(0);
      } catch (error) {
        instance.error('Error during graceful shutdown:', {
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        });
        process.exit(1);
      }
    };

    // Handle SIGTERM signal for graceful shutdown
    process.on('SIGTERM', () => {
      void shutdownGracefully('SIGTERM');
    });

    // Handle SIGINT signal for graceful shutdown
    process.on('SIGINT', () => {
      void shutdownGracefully('SIGINT');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      instance.error('Unhandled Rejection at:', {
        promise,
        reason,
        stack: reason instanceof Error ? reason.stack : undefined,
      });
      // Report to Sentry
      if (process.env.SENTRY_DSN_URL) {
        try {
          Sentry.captureException(reason);
        } catch (err) {
          instance.error('Failed to report error to Sentry:', err);
        }
      }
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      instance.error('Uncaught Exception:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      // Report to Sentry
      if (process.env.SENTRY_DSN_URL) {
        try {
          Sentry.captureException(error);
        } catch (err) {
          instance.error('Failed to report error to Sentry:', err);
        }
      }
      process.exit(1);
    });
  } catch (error) {
    instance.error('Error during application bootstrap:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    // Report to Sentry
    if (process.env.SENTRY_DSN_URL) {
      try {
        Sentry.captureException(error);
      } catch (err) {
        instance.error('Failed to report error to Sentry:', err);
      }
    }
    process.exit(1);
  }
}

// Execute the bootstrap function and handle any fatal errors
bootstrap().catch((error) => {
  console.error('Fatal error during bootstrap:', error);
  process.exit(1);
});
