import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { UiGateway } from './gateways/ui.gateway';
import { BackendWsService } from './services/backend-ws.service';
import { UserService } from '../common/helpers/user-service';
import { SessionsModule } from '../sessions/sessions.module';
import { ErrorHandlerService } from './services/error-handler.service';
import { ClientManager } from './managers/client.manager';
import { SessionManager } from './managers/session.manager';
import { BackendMessageTransformer } from './transformers/backend-message.transformer';
import { ChatRequestHandler } from './handlers/chat-request.handler';
import { InspirationRequestHandler } from './handlers/inspiration-request.handler';
import { CancelChatRequestHandler } from './handlers/cancel-chat-request.handler';
import { InitialSessionStateHandler } from './handlers/initial-session-state.handler';
import { ChatUpdateHandler } from './handlers/chat-update.handler';
import { InspirationStatusUpdateHandler } from './handlers/inspiration-status-update.handler';
import { ErrorHandler } from './handlers/error.handler';
import { GenericHandler } from './handlers/generic.handler';
import { SettingsRequestHandler } from './handlers/settings-request.handler';
import { SettingsUpdateHandler } from './handlers/settings-update.handler';
import { SessionNameRequestHandler } from './handlers/session-name-request.handler';
import { SessionNameUpdateHandler } from './handlers/session-name-update.handler';
import { FeedbackRequestHandler } from './handlers/feedback-request.handler';
import { FeedbackUpdateHandler } from './handlers/feedback-update.handler';

@Module({
  imports: [ConfigModule, HttpModule, SessionsModule],
  providers: [
    UiGateway,
    BackendWsService,
    UserService,
    ClientManager,
    SessionManager,
    ErrorHandlerService,
    BackendMessageTransformer,
    ChatRequestHandler,
    InspirationRequestHandler,
    CancelChatRequestHandler,
    InitialSessionStateHandler,
    ChatUpdateHandler,
    InspirationStatusUpdateHandler,
    ErrorHandler,
    GenericHandler,
    SettingsRequestHandler,
    SettingsUpdateHandler,
    SessionNameRequestHandler,
    SessionNameUpdateHandler,
    FeedbackRequestHandler,
    FeedbackUpdateHandler,
  ],
  exports: [UiGateway],
})
export class WebSocketModule {}
