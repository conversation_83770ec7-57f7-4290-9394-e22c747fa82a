import { Logger } from '@nestjs/common';
import {
  WebSocketGateway,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server } from 'ws';
import { IncomingMessage } from 'http';
import { WebSocket } from 'ws';
import { ConfigService } from '@nestjs/config';
import { UserService } from '../../common/helpers/user-service';
import { BackendWsService } from '../services/backend-ws.service';
import { v4 as uuidv4 } from 'uuid';
import {
  StandardErrorCode,
  BackendConnectionStatus,
  BackendStatusMessageDto,
  UiBackendStatusDto,
  DetailedUserResponse,
} from '../dto';
import { ErrorHandlerService } from '../services/error-handler.service';
import { ClientManager, WebSocketWithId } from '../managers/client.manager';
import { SessionManager } from '../managers/session.manager';
import { BackendMessageTransformer } from '../transformers/backend-message.transformer';
import { MessageHandler } from '../interfaces/message-handler.interface';
import { ChatRequestHandler } from '../handlers/chat-request.handler';
import { InspirationRequestHandler } from '../handlers/inspiration-request.handler';
import { CancelChatRequestHandler } from '../handlers/cancel-chat-request.handler';
import { SettingsRequestHandler } from '../handlers/settings-request.handler';
import { SessionNameRequestHandler } from '../handlers/session-name-request.handler';
import { FeedbackRequestHandler } from '../handlers/feedback-request.handler';
import { HttpException, OnModuleDestroy } from '@nestjs/common';

// Type Guard function
// Using 'unknown' is safer than 'any' for the input parameter
function isBackendStatusMessageDto(
  obj: unknown,
): obj is BackendStatusMessageDto {
  // Check if obj is a valid object before accessing properties
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }
  // Use type assertion carefully within the guard
  const potentialDto = obj as Record<string, unknown>;
  return (
    potentialDto.type === 'BACKEND_CONNECTION_STATUS' &&
    typeof potentialDto.session_id === 'string' &&
    typeof potentialDto.status === 'string' &&
    Object.values(BackendConnectionStatus).includes(
      potentialDto.status as BackendConnectionStatus,
    )
  );
}

/**
 * WebSocket gateway for UI clients connecting to the canvas
 */
@WebSocketGateway({
  path: '/api/v1/ui/canvas/websocket',
})
export class UiGateway
  implements
    OnGatewayConnection,
    OnGatewayDisconnect,
    OnGatewayInit,
    OnModuleDestroy
{
  @WebSocketServer() server: Server;

  private readonly logger = new Logger(UiGateway.name);
  private readonly messageHandlers: MessageHandler<any>[];
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private readonly heartbeatIntervalMs: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly backendWsService: BackendWsService,
    private readonly errorHandlerService: ErrorHandlerService,
    private readonly clientManager: ClientManager,
    private readonly sessionManager: SessionManager,
    private readonly messageTransformer: BackendMessageTransformer,
    chatRequestHandler: ChatRequestHandler,
    inspirationRequestHandler: InspirationRequestHandler,
    cancelChatRequestHandler: CancelChatRequestHandler,
    settingsRequestHandler: SettingsRequestHandler,
    sessionNameRequestHandler: SessionNameRequestHandler,
    feedbackRequestHandler: FeedbackRequestHandler,
  ) {
    this.logger.log('UiGateway constructor called');
    this.messageHandlers = [
      chatRequestHandler,
      inspirationRequestHandler,
      cancelChatRequestHandler,
      settingsRequestHandler,
      sessionNameRequestHandler,
      feedbackRequestHandler,
    ];

    // Get the heartbeat interval from config with default of 10 seconds
    this.heartbeatIntervalMs = this.configService.get<number>(
      'WEBSOCKET_HEARTBEAT_INTERVAL_MS',
      10000,
    );
    this.logger.log(
      `WebSocket heartbeat interval set to ${this.heartbeatIntervalMs}ms`,
    );
  }

  /**
   * Cleanup resources when module is destroyed
   */
  onModuleDestroy(): void {
    this.stopHeartbeat();
    this.logger.log('WebSocket Gateway destroyed, heartbeat stopped');
  }

  handleDisconnect(client: WebSocketWithId): void {
    const clientId = client.clientId;
    if (!clientId) {
      this.logger.warn(
        'Disconnect event received for client without known clientId!',
      );
      return;
    }
    this.logger.log(`[${clientId}] handleDisconnect - Client disconnected.`);
    this.cleanupClientResources(clientId);
  }

  /**
   * Called after the gateway is initialized.
   */
  afterInit(): void {
    this.logger.log('WebSocket Gateway initialized');

    // Configure WebSocket integration with Backend
    // Use a type-safe wrapper function to satisfy the TypeScript compiler
    const typeSafeHandler = (
      sessionId: string,
      message: Record<string, unknown>,
    ): void => {
      this.handleBackendMessage(sessionId, message);
    };
    this.backendWsService.setClientGlobalHandler(typeSafeHandler);

    // Set up raw message listener for all UI client connections
    this.server.on('connection', (client: WebSocketWithId) => {
      client.on('message', (data: Buffer | string) => {
        void this.routeIncomingMessage(client, data);
      });
    });

    // Start the heartbeat timer
    this.startHeartbeat();
  }

  /**
   * Starts the heartbeat timer to periodically ping clients
   */
  private startHeartbeat(): void {
    this.logger.log(
      `Starting WebSocket heartbeat (interval: ${this.heartbeatIntervalMs}ms)`,
    );
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, this.heartbeatIntervalMs);
  }

  /**
   * Stops the heartbeat timer
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
      this.logger.log('WebSocket heartbeat stopped');
    }
  }

  /**
   * Sends a ping to all connected clients
   */
  private sendHeartbeat(): void {
    const clientCount = this.clientManager.getClientCount();
    this.logger.debug(`Sending heartbeat ping to ${clientCount} clients`);

    let successCount = 0;
    let failedCount = 0;

    // Iterate through all clients and send a ping
    this.clientManager.getAllClientIdsArray().forEach((clientId) => {
      const client = this.clientManager.getClientSocket(clientId);
      if (client && client.readyState === WebSocket.OPEN) {
        try {
          client.ping();
          successCount++;
        } catch (error) {
          failedCount++;
          this.logger.warn(
            `Failed to send heartbeat to client ${clientId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
          // This client may be in a bad state, consider cleanup if needed
        }
      }
    });

    if (clientCount > 0) {
      this.logger.debug(
        `Heartbeat sent to ${successCount}/${clientCount} clients (${failedCount} failed)`,
      );
    }
  }

  private async routeIncomingMessage(
    client: WebSocketWithId,
    data: Buffer | string,
  ): Promise<void> {
    let parsedMessage: Record<string, unknown> | null = null;
    const clientId = client.clientId;
    let handlerFound = false;

    try {
      const rawString = typeof data === 'string' ? data : data.toString();
      parsedMessage = JSON.parse(rawString) as Record<string, unknown>;
      const messageType =
        typeof parsedMessage?.type === 'string'
          ? parsedMessage.type
          : undefined;

      if (!messageType) {
        this.logger.warn(`[${clientId}] Received message without type.`);
        this.errorHandlerService.sendErrorToClient(
          client,
          'Invalid message format: missing type',
          StandardErrorCode.INVALID_REQUEST,
        );
        return;
      }

      this.logger.debug(`[${clientId}] Routing message of type ${messageType}`);

      for (const handler of this.messageHandlers) {
        if (handler.canHandle(parsedMessage)) {
          handlerFound = true;
          this.logger.debug(
            `[${clientId}] Found handler for type ${messageType}: ${handler.constructor.name}`,
          );
          try {
            // Type assertion to ensure type safety
            const validatedDto = (await handler.validate(
              parsedMessage,
            )) as Record<string, unknown>;
            this.logger.debug(
              `[${clientId}] Message validated successfully by ${handler.constructor.name}`,
            );
            await handler.handle(client, validatedDto);
            this.logger.debug(
              `[${clientId}] Message handled successfully by ${handler.constructor.name}`,
            );
          } catch (validationOrHandleError: unknown) {
            const clientData = this.clientManager.getClientData(clientId);
            const sessionId =
              clientData?.sessionId ||
              (typeof parsedMessage?.session_id === 'string'
                ? parsedMessage.session_id
                : undefined);
            const requestId =
              typeof parsedMessage?.request_id === 'string'
                ? parsedMessage.request_id
                : undefined;

            // Improved validation error detection
            if (
              validationOrHandleError instanceof HttpException &&
              validationOrHandleError.getStatus() === 400
            ) {
              this.logger.warn(
                `[${clientId}] Validation failed in ${handler.constructor.name}: ${
                  validationOrHandleError instanceof Error
                    ? validationOrHandleError.message
                    : 'Unknown error'
                }`,
              );
              // Extract validation messages if possible, otherwise use generic message
              let validationMessages: string | string[] = 'Validation error';

              if (validationOrHandleError instanceof HttpException) {
                const response = validationOrHandleError.getResponse();
                if (
                  typeof response === 'object' &&
                  response !== null &&
                  'message' in response
                ) {
                  validationMessages = response.message as string | string[];
                } else if (validationOrHandleError instanceof Error) {
                  validationMessages = validationOrHandleError.message;
                }
              }

              const errorMessage = Array.isArray(validationMessages)
                ? validationMessages.join(', ')
                : String(validationMessages);

              this.errorHandlerService.sendErrorToClient(
                client,
                `Validation failed: ${errorMessage}`,
                StandardErrorCode.INVALID_REQUEST,
                sessionId,
                requestId,
              );
            } else {
              // Handle other errors (e.g., from handler.handle)
              const errorMessage =
                validationOrHandleError instanceof Error
                  ? validationOrHandleError.message
                  : 'Unknown error';

              const errorStack =
                validationOrHandleError instanceof Error
                  ? validationOrHandleError.stack
                  : undefined;

              this.logger.error(
                `[${clientId}] Error during handling in ${handler.constructor.name}: ${errorMessage}`,
                errorStack,
              );
              this.errorHandlerService.sendErrorToClient(
                client,
                'Error processing request',
                StandardErrorCode.INTERNAL_SERVER_ERROR,
                sessionId,
                requestId,
              );
            }
          }
          break;
        }
      }

      if (!handlerFound) {
        this.logger.warn(
          `[${clientId}] No handler found for message type: ${messageType}`,
        );
        const clientData = this.clientManager.getClientData(clientId);
        this.errorHandlerService.sendErrorToClient(
          client,
          `Unknown message type: ${messageType}`,
          StandardErrorCode.INVALID_REQUEST,
          clientData?.sessionId,
          typeof parsedMessage?.request_id === 'string'
            ? parsedMessage.request_id
            : undefined,
        );
      }
    } catch (error) {
      this.logger.error(
        `[${clientId}] Failed to parse or route incoming message: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Error processing message',
        StandardErrorCode.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Handle new WebSocket connections
   * @param client WebSocket client
   * @param request HTTP request
   */
  async handleConnection(
    client: WebSocketWithId,
    request: IncomingMessage,
  ): Promise<void> {
    this.logger.log(
      'handleConnection: Start - New connection attempt detected.',
    );

    // --- Stage 1: Immediate setup (before async operations) ---
    // Generate unique ID and attach to client *immediately*
    const clientId = uuidv4();
    client.clientId = clientId;
    this.logger.debug(`[${clientId}] Assigned temporary clientId.`);

    // Store socket reference immediately
    this.clientManager.registerClient(client);

    try {
      this.logger.log(`[${clientId}] Processing connection details...`);
      this.logger.debug(`[${clientId}] Request URL: ${request.url}`);
      this.logger.debug(`[${clientId}] Host: ${request.headers.host}`);

      // Log all headers for debugging
      for (const [key, value] of Object.entries(request.headers)) {
        const headerValue = Array.isArray(value)
          ? value.join(', ')
          : String(value);
        this.logger.debug(`[${clientId}] Header ${key}: ${headerValue}`);
      }

      // Parse connection URL and get parameters
      let url: URL;
      try {
        url = new URL(request.url!, `ws://${request.headers.host}`);
        this.logger.debug(`[${clientId}] Parsed URL: ${url.toString()}`);
        this.logger.debug(`[${clientId}] Path: ${url.pathname}`);
        this.logger.debug(`[${clientId}] Query params: ${url.search}`);
      } catch (error) {
        this.logger.error(
          `[${clientId}] URL parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        this.clientManager.unregisterClient(clientId);
        client.close(1008, 'Invalid URL format');
        return;
      }

      // Get and log query parameters
      const token = url.searchParams.get('token');
      const sessionId = url.searchParams.get('session_id');

      this.logger.debug(`[${clientId}] Token present: ${!!token}`);
      if (token) {
        this.logger.debug(`[${clientId}] Token length: ${token.length}`);
      }
      this.logger.debug(`[${clientId}] Session ID: ${sessionId}`);

      // Validate required parameters
      if (!token) {
        this.logger.error(`[${clientId}] No authentication token provided`);
        this.clientManager.unregisterClient(clientId);
        client.close(1008, 'Authentication required');
        return;
      }

      if (!sessionId) {
        this.logger.error(`[${clientId}] No session ID provided`);
        this.clientManager.unregisterClient(clientId);
        client.close(1008, 'Session ID required');
        return;
      }

      // Store preliminary client data *before* await - THIS LOGIC IS MOVED TO ClientManager.registerClient
      // ClientManager.registerClient now initializes the basic structure.
      // The subsequent authenticateClient call will fill in the details.
      // REMOVED: this.clientManager.setClientData(clientId, preliminaryClientData);
      this.logger.debug(
        `[${clientId}] Client registered with preliminary status 'pending'. Authentication follows.`,
      );

      // --- Stage 2: Asynchronous operations (Authentication) ---
      // Authenticate the user
      let user: DetailedUserResponse | null = null;
      try {
        this.logger.debug(`[${clientId}] Validating token...`);
        user = await this.userService.getInternalUser(`Bearer ${token}`);
        this.logger.debug(`[${clientId}] User authenticated: ${user.id}`);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        const errorStack = error instanceof Error ? error.stack : '';
        this.logger.error(
          `[${clientId}] Authentication failed: ${errorMessage}`,
          errorStack
            ? errorStack.substring(0, 500)
            : 'No stack trace available',
        );
        this.clientManager.unregisterClient(clientId);
        client.close(1008, 'Authentication failed');
        return;
      }

      // --- Stage 3: Final setup (after successful async operations) ---
      // Check if authentication was successful and user object exists before proceeding
      if (!user) {
        this.logger.error(
          `[${clientId}] Authentication succeeded but user object is null. Aborting.`,
        );
        this.clientManager.unregisterClient(clientId);
        client.close(1011, 'Internal error during authentication');
        return;
      }

      // Access user.id only *after* successful try-catch and null check
      this.logger.debug(
        `[${clientId}] User ${user.id} authenticated. Proceeding with final client setup.`,
      );

      // --- Raw Message Listener for Debugging ---
      // Add this *after* status is 'ready' to avoid logging during pending state
      client.on('message', (rawData: Buffer) => {
        this.logger.debug(
          `[${clientId}] Raw message received: ${rawData.toString()}`,
        );
        // Note: This listener might log messages that are also handled by @SubscribeMessage
        // It's primarily for seeing if messages arrive at all.
      });

      // --- Check if client disconnected during authentication ---
      if (!this.clientManager.getClientSocket(clientId)) {
        this.logger.warn(
          `[${clientId}] Client disconnected during authentication. Aborting final setup.`,
        );
        // Clean up the session entry that might have been added prematurely if logic changes
        // (Current logic adds to sessionToClients below, so this is a safeguard)
        const sessionClientIds =
          this.sessionManager.getClientsInSession(sessionId);
        if (sessionClientIds) {
          sessionClientIds.forEach((clientId) => {
            if (clientId !== client.clientId) {
              this.sessionManager.removeClientFromSession(clientId, sessionId);
            }
          });
        }
        // Data map was already updated, but handleDisconnect should clean it if it ran.
        return; // Don't proceed to connect to backend etc.
      }

      // Use ClientManager.authenticateClient for final registration step
      this.clientManager.authenticateClient(clientId, user.id, sessionId);

      // Associate client with session
      this.sessionManager.addClientToSession(clientId, sessionId);

      this.backendWsService.connectToBackend(
        sessionId,
        user.id,
        (message: Record<string, unknown>) =>
          this.handleBackendMessage(sessionId, message),
      );

      this.logger.log(
        `Client connected for session ${sessionId}, user ${user.id}`,
      );

      // Add event handlers
      client.on('error', (error: Error) => {
        this.logger.error(`Client connection error: ${error.message}`);
      });
    } catch (error) {
      this.logger.error(
        `[${clientId}] Unexpected error during connection handling. Cleaning up.`,
      );
      this.clientManager.unregisterClient(clientId);

      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : '';
      this.logger.error(
        `[${clientId}] Unexpected connection error details: ${errorMessage}`,
        errorStack ? errorStack.substring(0, 500) : 'No stack trace available',
      );
      client.close(1011, 'Internal server error');
    }
  }

  /**
   * Updated handleBackendMessage to use the transformer and broadcast,
   * and handle internal status messages.
   */
  private handleBackendMessage(
    sessionId: string,
    message: Record<string, unknown>,
  ): void {
    this.logger.debug(
      `Received message from backend service for session ${sessionId}: ${JSON.stringify(message).substring(0, 200)}...`,
    );

    // Check for internal status messages first using the type guard
    if (isBackendStatusMessageDto(message)) {
      this.handleBackendStatusUpdate(message);
      return; // Don't process internal status messages further
    }

    // Proceed with transforming and broadcasting regular messages
    this.messageTransformer
      .transform(sessionId, message)
      .then((transformedMessage) => {
        if (!transformedMessage) {
          this.logger.debug(
            `[${sessionId}] Backend message transformation resulted in null, skipping send.`,
          );
          return;
        }

        const messageType = transformedMessage.type;
        this.logger.debug(
          `[${sessionId}] Broadcasting transformed ${messageType} message to session`,
        );

        // Pass the specific DTO type directly to broadcastToSession
        // The method will handle stringifying it.
        this.broadcastToSession(sessionId, transformedMessage);
      })
      .catch((error) => {
        this.logger.error(
          `[${sessionId}] Error transforming or broadcasting backend message: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        this.broadcastErrorToSession(
          sessionId,
          'Error processing backend update',
          StandardErrorCode.INTERNAL_SERVER_ERROR,
        );
      });
  }

  /**
   * Handles internal backend connection status updates and notifies UI clients.
   */
  private handleBackendStatusUpdate(
    statusMessage: BackendStatusMessageDto,
  ): void {
    const sessionId = statusMessage.session_id;
    this.logger.log(
      `[${sessionId}] Processing backend status update: ${statusMessage.status}`,
    );

    // Map internal status to UI status
    let uiStatus: UiBackendStatusDto['status'];
    switch (statusMessage.status) {
      case BackendConnectionStatus.CONNECTED:
      case BackendConnectionStatus.RECONNECTED:
        uiStatus = 'CONNECTED';
        break;
      case BackendConnectionStatus.RECONNECTING:
        uiStatus = 'RECONNECTING';
        break;
      case BackendConnectionStatus.DISCONNECTED:
        uiStatus = 'DISCONNECTED';
        break;
      case BackendConnectionStatus.FAILED:
        uiStatus = 'FAILED';
        break;
      default:
        this.logger.warn(
          `[${sessionId}] Received unhandled backend status: ${statusMessage.status}`,
        );
        return; // Don't send unknown status to UI
    }

    const uiMessage: UiBackendStatusDto = {
      type: 'BACKEND_STATUS',
      status: uiStatus,
      message: statusMessage.message, // Forward the message if present
    };

    // Pass the specific UI DTO directly
    this.broadcastToSession(sessionId, uiMessage);
  }

  /**
   * Broadcasts a message to all clients in a specific session.
   * Accepts any object that has a 'type' property (or defaults to UNKNOWN).
   * @param sessionId The session to broadcast to.
   * @param message The message object to send.
   */
  private broadcastToSession(
    sessionId: string,
    message: { type?: string; [key: string]: any }, // Accept object with optional type
  ): void {
    const sessionClients = this.sessionManager.getClientsInSession(sessionId);
    const messageType = message.type || 'UNKNOWN';

    if (sessionClients && sessionClients.size > 0) {
      // Stringify the received object directly
      const messageString = JSON.stringify(message);
      this.logger.debug(
        `Broadcasting message type '${messageType}' to ${sessionClients.size} clients in session ${sessionId}`,
      );
      sessionClients.forEach((clientId) => {
        const clientSocket = this.clientManager.getClientSocket(clientId);
        if (clientSocket && clientSocket.readyState === WebSocket.OPEN) {
          try {
            clientSocket.send(messageString);
            this.logger.debug(
              `Sent '${messageType}' to client ${clientId} in session ${sessionId}`,
            );
          } catch (error) {
            this.logger.error(
              `Failed to send message type '${messageType}' to client ${clientId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
            );
            // Consider removing client if send fails repeatedly
          }
        } else {
          this.logger.warn(
            `Client ${clientId} socket not found or not open during broadcast of type '${messageType}'.`,
          );
        }
      });
    } else {
      this.logger.warn(
        `No clients found for session ${sessionId} to broadcast message type '${messageType}'.`,
      );
    }
  }

  /**
   * Broadcasts a standardized error message to all clients in a session.
   * @param sessionId The session ID.
   * @param message Error message string.
   * @param code Standard error code.
   * @param requestId Optional request ID if the error is related to a specific request.
   */
  private broadcastErrorToSession(
    sessionId: string,
    message: string,
    code: StandardErrorCode,
    requestId?: string,
  ): void {
    this.logger.warn(
      `Broadcasting error to session ${sessionId}: Code=${code}, Msg=${message}`,
    );
    const sessionClients = this.sessionManager.getClientsInSession(sessionId);
    if (sessionClients && sessionClients.size > 0) {
      sessionClients.forEach((clientId) => {
        const clientSocket = this.clientManager.getClientSocket(clientId);
        if (clientSocket) {
          this.errorHandlerService.sendErrorToClient(
            clientSocket,
            message,
            code,
            sessionId,
            requestId,
          );
        } else {
          this.logger.warn(
            `Socket for client ${clientId} not found when broadcasting error to session ${sessionId}.`,
          );
        }
      });
    } else {
      this.logger.warn(
        `No clients found for session ${sessionId} to broadcast error to.`,
      );
    }
  }

  /**
   * Helper function to safely stringify objects with potential circular references
   */
  private safeJsonReplacer() {
    const seen = new WeakSet();
    return (key: string, value: unknown) => {
      if (typeof value === 'object' && value !== null) {
        // Type assertion for WeakSet operations
        if (seen.has(value)) {
          return '[Circular Reference]';
        }
        seen.add(value);
      }
      return value;
    };
  }

  private cleanupClientResources(clientId: string): void {
    const logPrefix = `[${clientId}] cleanupClientResources -`;
    this.logger.log(`${logPrefix} Starting cleanup.`);

    const clientData = this.clientManager.unregisterClient(clientId);

    if (clientData) {
      this.logger.debug(
        `${logPrefix} Found client data (status: ${clientData.status}).`,
      );
      // Use SessionManager for session cleanup logic
      if (clientData.status === 'ready' && clientData.sessionId) {
        this.sessionManager.removeClientFromSession(
          clientId,
          clientData.sessionId,
        );
      } else {
        this.logger.log(
          `${logPrefix} Client status was '${clientData.status}' or sessionId missing. Skipping session cleanup.`,
        );
      }
    } else {
      this.logger.warn(`${logPrefix} Client data not found during cleanup.`);
    }
    this.logger.log(`${logPrefix} Cleanup finished.`);
  }
}
