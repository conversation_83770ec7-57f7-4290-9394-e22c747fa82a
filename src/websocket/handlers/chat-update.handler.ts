import { Injectable, Logger } from '@nestjs/common';
import { IBackendMessageHandler } from './backend-message-handler.interface';
import { UiOutputMessage } from '../dto/ui-output-message.dto';
import { SessionsService } from '../../sessions/sessions.service';
import { ClientManager } from '../managers/client.manager';
import { SessionManager } from '../managers/session.manager';
import {
  BackendChatMessage,
  BackendChatResponse,
} from '../dto/backend-message.dto';
import { ChatUpdateDto, ChatUpdateResponse } from '../dto/session-message.dto';
import {
  BackendMessageType,
  UiOutputMessageType,
} from '../constants/message-types.enum';

@Injectable()
export class ChatUpdateHandler implements IBackendMessageHandler {
  private readonly logger = new Logger(ChatUpdateHandler.name);

  constructor(
    private readonly sessionsService: SessionsService,
    private readonly clientManager: ClientManager,
    private readonly sessionManager: SessionManager,
  ) {}

  canHandle(message: Record<string, unknown>): boolean {
    // Check for type or specific fields indicative of CHAT_UPDATE
    return (
      message.type === BackendMessageType.ChatUpdate ||
      ('responses' in message && Array.isArray(message.responses)) ||
      'artifacts' in message ||
      ('done' in message && message.done === true)
    );
  }

  transform(
    sessionId: string,
    message: Record<string, unknown>,
  ): Promise<UiOutputMessage | null> {
    this.logger.debug(`[${sessionId}] Handling S2C_CHAT`);
    const backendUpdate = message as Partial<BackendChatMessage>;
    const currentlyStreamingRule =
      this.sessionManager.getStreamingRuleState(sessionId);

    const { isStreamingRule, responses } =
      this.getResponsesWithoutStreamingRule(
        currentlyStreamingRule,
        backendUpdate.payload?.responses ?? [],
      );

    // Update the streaming rule state in the session manager
    this.sessionManager.setStreamingRuleState(sessionId, isStreamingRule);

    const uiUpdate: ChatUpdateDto = {
      type: UiOutputMessageType.ChatUpdate,
      session_id: sessionId,
      request_id: backendUpdate.request_id,
      timestamp: backendUpdate.timestamp,
      responses: responses.map((response) => this.transformResponse(response)),
      artifacts: backendUpdate.payload?.artifacts,
      done: backendUpdate.payload?.done,
      status: backendUpdate.payload?.status,
      error: backendUpdate.error,
    };

    return Promise.resolve(uiUpdate);
  }

  private getResponsesWithoutStreamingRule(
    isStreamingRule: boolean,
    responses: BackendChatResponse[],
  ): { isStreamingRule: boolean; responses: BackendChatResponse[] } {
    const responsesWithoutStreamingRule: BackendChatResponse[] = [];
    for (const response of responses) {
      if (isStreamingRule) {
        if (response.content?.endsWith('```')) {
          isStreamingRule = false;
        }
        continue;
      }

      if (response.content?.startsWith('```')) {
        isStreamingRule = true;
      } else {
        responsesWithoutStreamingRule.push(response);
      }
    }
    return { isStreamingRule, responses: responsesWithoutStreamingRule };
  }

  private transformResponse(response: BackendChatResponse): ChatUpdateResponse {
    return {
      id: response.id,
      type: response.type,
      content: response.content,
      artifacts: response.artifacts,
      status: response.status,
      done: response.done,
    };
  }
}
