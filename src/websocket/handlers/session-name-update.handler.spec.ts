import { Test, TestingModule } from '@nestjs/testing';
import { SessionNameUpdateHandler } from './session-name-update.handler';
import { SessionsService } from '../../sessions/sessions.service';
import { ClientManager } from '../managers/client.manager';
import { BackendSessionNameMessage } from '../dto/backend-message.dto';
import { ChatUpdateDto } from '../dto/session-message.dto';
import { CanvasError } from '../dto/common.dto';
import {
  BackendMessageType,
  UiOutputMessageType,
} from '../constants/message-types.enum';
import { v4 as uuidv4 } from 'uuid';

// Mock ClientManager
const mockClientManager = {
  getUserIdForSession: jest.fn(),
};

// Mock SessionsService
const mockSessionsService = {
  updateSessionTitle: jest.fn(),
};

describe('SessionNameUpdateHandler', () => {
  let handler: SessionNameUpdateHandler;
  let clientManager: ClientManager;
  let sessionsService: SessionsService;

  beforeEach(async () => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SessionNameUpdateHandler,
        { provide: SessionsService, useValue: mockSessionsService },
        { provide: ClientManager, useValue: mockClientManager },
      ],
    }).compile();

    handler = module.get<SessionNameUpdateHandler>(SessionNameUpdateHandler);
    clientManager = module.get<ClientManager>(ClientManager);
    sessionsService = module.get<SessionsService>(SessionsService);

    // Suppress console logs during tests for cleaner output
    jest.spyOn(handler['logger'], 'log').mockImplementation(() => {});
    jest.spyOn(handler['logger'], 'debug').mockImplementation(() => {});
    jest.spyOn(handler['logger'], 'warn').mockImplementation(() => {});
    jest.spyOn(handler['logger'], 'error').mockImplementation(() => {});

    // Default mock implementations
    mockClientManager.getUserIdForSession.mockReturnValue('test-user-id');
    mockSessionsService.updateSessionTitle.mockResolvedValue(undefined);
  });

  it('should be defined', () => {
    expect(handler).toBeDefined();
  });

  describe('canHandle', () => {
    it('should return true for BackendMessageType.SessionNameUpdate', () => {
      const message = { type: BackendMessageType.SessionNameUpdate };
      expect(handler.canHandle(message)).toBe(true);
    });

    it('should return true for messages with payload.session_name', () => {
      const message = {
        payload: {
          session_name: 'Test Session Name',
        },
      };
      expect(handler.canHandle(message)).toBe(true);
    });

    it('should return false for other message types', () => {
      const message = { type: BackendMessageType.ChatUpdate };
      expect(handler.canHandle(message)).toBe(false);
    });

    it('should return false for messages without payload.session_name', () => {
      const message = {
        payload: {
          some_other_field: 'value',
        },
      };
      expect(handler.canHandle(message)).toBe(false);
    });

    it('should return false for messages without payload', () => {
      const message = { type: 'SOME_OTHER_TYPE' };
      expect(handler.canHandle(message)).toBe(false);
    });

    it('should return false for empty messages', () => {
      const message = {};
      expect(handler.canHandle(message)).toBe(false);
    });

    it('should return false for messages with null payload', () => {
      const message = { payload: null };
      expect(handler.canHandle(message)).toBe(false);
    });
  });

  describe('transform', () => {
    const sessionId = 'test-session-id';
    const userId = 'test-user-id';
    const sessionName = 'Generated Session Name';
    const requestId = uuidv4();
    const timestamp = new Date().toISOString();

    beforeEach(() => {
      mockClientManager.getUserIdForSession.mockReturnValue(userId);
    });

    it('should successfully transform a valid session name update message', async () => {
      const backendMessage: BackendSessionNameMessage = {
        type: BackendMessageType.SessionNameUpdate,
        payload: {
          session_name: sessionName,
        },
        request_id: requestId,
        timestamp,
      };

      const expectedUiUpdate: ChatUpdateDto = {
        type: UiOutputMessageType.ChatUpdate,
        session_id: sessionId,
        request_id: requestId,
        timestamp,
        responses: [],
        generated_session_name: sessionName,
        error: undefined,
      };

      const result = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      expect(result).toEqual(expectedUiUpdate);
      expect(clientManager.getUserIdForSession).toHaveBeenCalledWith(sessionId);
      expect(sessionsService.updateSessionTitle).toHaveBeenCalledWith(
        userId,
        sessionId,
        sessionName,
        true,
      );
    });

    it('should return null when userId is missing', async () => {
      mockClientManager.getUserIdForSession.mockReturnValue(null);

      const backendMessage: BackendSessionNameMessage = {
        type: BackendMessageType.SessionNameUpdate,
        payload: {
          session_name: sessionName,
        },
        request_id: requestId,
        timestamp,
      };

      const result = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      expect(result).toBeNull();
      expect(clientManager.getUserIdForSession).toHaveBeenCalledWith(sessionId);
      expect(sessionsService.updateSessionTitle).not.toHaveBeenCalled();
    });

    it('should return null when sessionName is missing', async () => {
      const backendMessage: BackendSessionNameMessage = {
        type: BackendMessageType.SessionNameUpdate,
        payload: {
          session_name: undefined as any,
        },
        request_id: requestId,
        timestamp,
      };

      const result = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      expect(result).toBeNull();
      expect(clientManager.getUserIdForSession).toHaveBeenCalledWith(sessionId);
      expect(sessionsService.updateSessionTitle).not.toHaveBeenCalled();
    });

    it('should return null when sessionName is empty string', async () => {
      const backendMessage: BackendSessionNameMessage = {
        type: BackendMessageType.SessionNameUpdate,
        payload: {
          session_name: '',
        },
        request_id: requestId,
        timestamp,
      };

      const result = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      expect(result).toBeNull();
      expect(clientManager.getUserIdForSession).toHaveBeenCalledWith(sessionId);
      expect(sessionsService.updateSessionTitle).not.toHaveBeenCalled();
    });

    it('should handle missing payload gracefully', async () => {
      const backendMessage: Record<string, unknown> = {
        type: BackendMessageType.SessionNameUpdate,
        request_id: requestId,
        timestamp,
      };

      const result = await handler.transform(sessionId, backendMessage);

      expect(result).toBeNull();
      expect(clientManager.getUserIdForSession).toHaveBeenCalledWith(sessionId);
      expect(sessionsService.updateSessionTitle).not.toHaveBeenCalled();
    });

    it('should handle message with error property', async () => {
      const error: CanvasError = { code: 500, message: 'Test error message' };
      const backendMessage: BackendSessionNameMessage = {
        type: BackendMessageType.SessionNameUpdate,
        payload: {
          session_name: sessionName,
        },
        request_id: requestId,
        timestamp,
        error,
      };

      const expectedUiUpdate: ChatUpdateDto = {
        type: UiOutputMessageType.ChatUpdate,
        session_id: sessionId,
        request_id: requestId,
        timestamp,
        responses: [],
        generated_session_name: sessionName,
        error,
      };

      const result = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      expect(result).toEqual(expectedUiUpdate);
      expect(sessionsService.updateSessionTitle).toHaveBeenCalledWith(
        userId,
        sessionId,
        sessionName,
        true,
      );
    });

    it('should handle message without request_id and timestamp', async () => {
      const backendMessage: Record<string, unknown> = {
        type: BackendMessageType.SessionNameUpdate,
        payload: {
          session_name: sessionName,
        },
        timestamp: new Date().toISOString(),
      };

      const expectedUiUpdate: ChatUpdateDto = {
        type: UiOutputMessageType.ChatUpdate,
        session_id: sessionId,
        request_id: undefined,
        timestamp: backendMessage.timestamp as string,
        responses: [],
        generated_session_name: sessionName,
        error: undefined,
      };

      const result = await handler.transform(sessionId, backendMessage);

      expect(result).toEqual(expectedUiUpdate);
      expect(sessionsService.updateSessionTitle).toHaveBeenCalledWith(
        userId,
        sessionId,
        sessionName,
        true,
      );
    });

    it('should proceed with transformation even if updateSessionTitle fails', async () => {
      const updateError = new Error('Database update failed');
      mockSessionsService.updateSessionTitle.mockRejectedValue(updateError);

      const backendMessage: BackendSessionNameMessage = {
        type: BackendMessageType.SessionNameUpdate,
        payload: {
          session_name: sessionName,
        },
        request_id: requestId,
        timestamp,
      };

      const expectedUiUpdate: ChatUpdateDto = {
        type: UiOutputMessageType.ChatUpdate,
        session_id: sessionId,
        request_id: requestId,
        timestamp,
        responses: [],
        generated_session_name: sessionName,
        error: undefined,
      };

      const result = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      expect(result).toEqual(expectedUiUpdate);
      expect(sessionsService.updateSessionTitle).toHaveBeenCalledWith(
        userId,
        sessionId,
        sessionName,
        true,
      );
    });

    it('should log error when updateSessionTitle fails', async () => {
      const updateError = new Error('Database update failed');
      mockSessionsService.updateSessionTitle.mockRejectedValue(updateError);
      const loggerErrorSpy = jest.spyOn(handler['logger'], 'error');

      const backendMessage: BackendSessionNameMessage = {
        type: BackendMessageType.SessionNameUpdate,
        payload: {
          session_name: sessionName,
        },
        request_id: requestId,
        timestamp,
      };

      await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      // Wait for the async updateSessionTitle to complete
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(loggerErrorSpy).toHaveBeenCalledWith(
        `[${sessionId}] Error updating session name: ${updateError}`,
      );
    });

    it('should log error when userId is missing', async () => {
      mockClientManager.getUserIdForSession.mockReturnValue(null);
      const loggerErrorSpy = jest.spyOn(handler['logger'], 'error');

      const backendMessage: BackendSessionNameMessage = {
        type: BackendMessageType.SessionNameUpdate,
        payload: {
          session_name: sessionName,
        },
        request_id: requestId,
        timestamp,
      };

      await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      expect(loggerErrorSpy).toHaveBeenCalledWith(
        `[${sessionId}] Invalid session name update: sessionName=${sessionName} userId=null`,
      );
    });

    it('should log error when sessionName is missing', async () => {
      const loggerErrorSpy = jest.spyOn(handler['logger'], 'error');

      const backendMessage: BackendSessionNameMessage = {
        type: BackendMessageType.SessionNameUpdate,
        payload: {
          session_name: undefined as any,
        },
        request_id: requestId,
        timestamp,
      };

      await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      expect(loggerErrorSpy).toHaveBeenCalledWith(
        `[${sessionId}] Invalid session name update: sessionName=undefined userId=${userId}`,
      );
    });

    it('should log debug message when handling session name update', async () => {
      const loggerDebugSpy = jest.spyOn(handler['logger'], 'debug');

      const backendMessage: BackendSessionNameMessage = {
        type: BackendMessageType.SessionNameUpdate,
        payload: {
          session_name: sessionName,
        },
        request_id: requestId,
        timestamp,
      };

      await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      expect(loggerDebugSpy).toHaveBeenCalledWith(
        `[${sessionId}] Handling S2C_SESSION_NAME`,
      );
    });

    it('should log debug message when session name is successfully updated', async () => {
      const loggerDebugSpy = jest.spyOn(handler['logger'], 'debug');

      const backendMessage: BackendSessionNameMessage = {
        type: BackendMessageType.SessionNameUpdate,
        payload: {
          session_name: sessionName,
        },
        request_id: requestId,
        timestamp,
      };

      await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      // Wait for the async updateSessionTitle to complete
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(loggerDebugSpy).toHaveBeenCalledWith(
        `[${sessionId}] Session name updated: ${sessionName}`,
      );
    });

    it('should handle message with partial BackendSessionNameMessage structure', async () => {
      const partialMessage: Record<string, unknown> = {
        payload: {
          session_name: sessionName,
        },
        request_id: requestId,
        timestamp,
      };

      const expectedUiUpdate: ChatUpdateDto = {
        type: UiOutputMessageType.ChatUpdate,
        session_id: sessionId,
        request_id: requestId,
        timestamp,
        responses: [],
        generated_session_name: sessionName,
        error: undefined,
      };

      const result = await handler.transform(sessionId, partialMessage);

      expect(result).toEqual(expectedUiUpdate);
      expect(sessionsService.updateSessionTitle).toHaveBeenCalledWith(
        userId,
        sessionId,
        sessionName,
        true,
      );
    });
  });
});
