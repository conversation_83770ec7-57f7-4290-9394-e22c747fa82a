import { Test, TestingModule } from '@nestjs/testing';
import {
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { ChatRequestHandler } from './chat-request.handler';
import { ClientManager, WebSocketWithId } from '../managers/client.manager';
import { SessionManager } from '../managers/session.manager';
import { ErrorHandlerService } from '../services/error-handler.service';
import { BackendWsService } from '../services/backend-ws.service';
import { SessionsService } from '../../sessions/sessions.service';
import { ChatRequestDto } from '../dto/chat-request.dto';
import { StandardErrorCode } from '../dto/common.dto';
import { SessionMetadataDto } from '../../sessions/dto';
import { v4 as uuidv4 } from 'uuid';
import { ArtifactsDto } from '../dto/common.dto';
import { SupportedLanguage } from 'src/common/languages';

// Mock WebSocket client
const mockWebSocketClient = {
  clientId: 'client-123',
  // Add other necessary WebSocket properties/methods if needed by handlers
} as WebSocketWithId;

// Mock ClientData
const mockClientData = {
  userId: 'user-abc',
  sessionId: 'session-xyz',
  status: 'ready',
  authenticated: true,
};

// Mock SessionMetadata
const mockSessionMetadata: SessionMetadataDto = {
  session_id: 'session-xyz',
  session_title: 'Test Session',
  language: SupportedLanguage.SIGMA,
  language_locked: false,
  title_generated: false,
  created_at: new Date().toISOString(),
  last_updated_at: new Date().toISOString(),
  is_bookmarked: false,
};

describe('ChatRequestHandler', () => {
  let handler: ChatRequestHandler;
  let mockClientManager: Partial<ClientManager>;
  let mockSessionManager: Partial<SessionManager>;
  let mockErrorHandlerService: Partial<ErrorHandlerService>;
  let mockBackendWsService: Partial<BackendWsService>;
  let mockSessionsService: Partial<SessionsService>;

  // Helper function to create a message with specific language properties
  const createMessage = (
    lang?: SupportedLanguage,
    artifacts?: ArtifactsDto,
    inspirations?: string[],
    prompt = 'Test prompt',
    timezone?: string,
  ): ChatRequestDto => ({
    type: 'CHAT_REQUEST',
    session_id: mockClientData.sessionId,
    request_id: uuidv4(),
    prompt: prompt,
    language: lang,
    artifacts: artifacts ?? {},
    inspiration_ids: inspirations,
    timezone: timezone,
  });

  // Helper function to create session metadata
  const createSessionMeta = (
    lang?: SupportedLanguage | null,
    timezone?: string,
  ): SessionMetadataDto => ({
    ...mockSessionMetadata, // Base mock
    language: lang as SupportedLanguage, // Cast to expected type, handle null logic upstream if needed
    timezone: timezone,
  });

  beforeEach(async () => {
    // Create mocks for dependencies
    mockClientManager = {
      getClientData: jest.fn().mockReturnValue(mockClientData),
    };
    mockSessionManager = {}; // Add methods if needed
    mockErrorHandlerService = {
      sendErrorToClient: jest.fn(),
    };
    mockBackendWsService = {
      sendChatRequest: jest.fn(),
      sendSettingsRequest: jest.fn(),
      sendSessionNameRequest: jest.fn(),
    };
    mockSessionsService = {
      getSessionById: jest.fn().mockResolvedValue(mockSessionMetadata),
      updateSessionInspirations: jest.fn().mockResolvedValue(undefined),
      updateSessionMetadata: jest
        .fn()
        .mockResolvedValue(createSessionMeta(SupportedLanguage.SIGMA)),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChatRequestHandler,
        { provide: ClientManager, useValue: mockClientManager },
        { provide: SessionManager, useValue: mockSessionManager }, // Provide if needed
        { provide: ErrorHandlerService, useValue: mockErrorHandlerService },
        { provide: BackendWsService, useValue: mockBackendWsService },
        { provide: SessionsService, useValue: mockSessionsService },
      ],
    }).compile();

    handler = module.get<ChatRequestHandler>(ChatRequestHandler);

    // Suppress console logs during tests for cleaner output
    jest.spyOn(handler['logger'], 'log').mockImplementation(() => {});
    jest.spyOn(handler['logger'], 'debug').mockImplementation(() => {});
    jest.spyOn(handler['logger'], 'warn').mockImplementation(() => {});
    jest.spyOn(handler['logger'], 'error').mockImplementation(() => {});

    mockSessionsService = module.get<SessionsService>(SessionsService);
    mockBackendWsService = module.get<BackendWsService>(BackendWsService);
    mockErrorHandlerService =
      module.get<ErrorHandlerService>(ErrorHandlerService);

    // Default mock for getSessionById used in most tests
    mockSessionsService.getSessionById = jest
      .fn()
      .mockResolvedValue(createSessionMeta(SupportedLanguage.SIGMA)); // Default: sigma

    // Default mock for the new service method
    mockSessionsService.updateSessionMetadata = jest
      .fn()
      .mockResolvedValue(createSessionMeta(SupportedLanguage.SIGMA)); // Returns updated session

    mockBackendWsService.sendChatRequest = jest.fn();
    mockBackendWsService.sendSettingsRequest = jest.fn();
    mockBackendWsService.sendSessionNameRequest = jest.fn();
    mockErrorHandlerService.sendErrorToClient = jest.fn();
  });

  it('should be defined', () => {
    expect(handler).toBeDefined();
  });

  describe('canHandle', () => {
    it('should return true for CHAT_REQUEST type', () => {
      expect(handler.canHandle({ type: 'CHAT_REQUEST' })).toBe(true);
    });

    it('should return false for other types', () => {
      expect(handler.canHandle({ type: 'OTHER_TYPE' })).toBe(false);
      expect(handler.canHandle({})).toBe(false);
    });
  });

  // Validate method tests (basic)
  describe('validate', () => {
    it('should pass validation for a valid ChatRequestDto', async () => {
      // Revert to plain object - artifacts: {} should be valid now it's optional
      const validMessage = {
        type: 'CHAT_REQUEST',
        session_id: uuidv4(),
        prompt: 'Test prompt',
        request_id: uuidv4(),
        artifacts: {}, // Empty object should be acceptable for optional nested DTO
      };
      await expect(handler.validate(validMessage)).resolves.toBeDefined();
    });

    it('should fail validation for an invalid message', async () => {
      const invalidMessage = { type: 'CHAT_REQUEST', extraField: 'disallowed' };
      await expect(handler.validate(invalidMessage)).rejects.toThrow();
    });
  });

  describe('handle', () => {
    let validMessage: ChatRequestDto;

    beforeEach(() => {
      // Reset mocks before each test in this block
      jest.clearAllMocks();
      // Re-mock default behaviors
      mockClientManager.getClientData = jest
        .fn()
        .mockReturnValue(mockClientData);
      mockSessionsService.getSessionById = jest
        .fn()
        .mockResolvedValue(mockSessionMetadata);
      mockSessionsService.updateSessionInspirations = jest
        .fn()
        .mockResolvedValue(undefined);
      mockSessionsService.updateSessionMetadata = jest
        .fn()
        .mockResolvedValue(createSessionMeta(SupportedLanguage.SIGMA)); // Returns updated session

      mockBackendWsService.sendChatRequest = jest.fn();
      mockBackendWsService.sendSettingsRequest = jest.fn();
      mockBackendWsService.sendSessionNameRequest = jest.fn();
      mockErrorHandlerService.sendErrorToClient = jest.fn();

      // Initialize validMessage within beforeEach
      validMessage = createMessage(); // Basic message without language
    });

    it('should send error if client is not ready/authenticated', async () => {
      (mockClientManager.getClientData as jest.Mock).mockReturnValueOnce({
        ...mockClientData,
        status: 'connecting',
      });
      await handler.handle(mockWebSocketClient, validMessage);
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        expect.any(String),
        StandardErrorCode.UNAUTHORIZED,
        validMessage.session_id,
        validMessage.request_id,
      );
      expect(mockBackendWsService.sendChatRequest).not.toHaveBeenCalled();
    });

    it('should send error if session ID mismatches', async () => {
      validMessage.session_id = 'different-session';
      await handler.handle(mockWebSocketClient, validMessage);
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        expect.any(String),
        StandardErrorCode.INVALID_REQUEST,
        mockClientData.sessionId, // Should use clientData session id
        validMessage.request_id,
      );
      expect(mockBackendWsService.sendChatRequest).not.toHaveBeenCalled();
    });

    it('should call updateSessionInspirations if inspiration_ids are present', async () => {
      const messageWithInspirations = {
        ...validMessage,
        inspiration_ids: ['insp1', 'insp2'],
      };
      await handler.handle(mockWebSocketClient, messageWithInspirations);

      expect(
        mockSessionsService.updateSessionInspirations,
      ).toHaveBeenCalledTimes(1);
      expect(
        mockSessionsService.updateSessionInspirations,
      ).toHaveBeenCalledWith(mockClientData.userId, mockClientData.sessionId, [
        'insp1',
        'insp2',
      ]);
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
    });

    it('should NOT call updateSessionInspirations if inspiration_ids are missing', async () => {
      expect(validMessage).toBeDefined();
      await handler.handle(mockWebSocketClient, validMessage);
      expect(
        mockSessionsService.updateSessionInspirations,
      ).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
    });

    it('should proceed if updateSessionInspirations fails but log error', async () => {
      const loggerErrorSpy = jest.spyOn(handler['logger'], 'error');
      const updateError = new Error('Update failed');
      (
        mockSessionsService.updateSessionInspirations as jest.Mock
      ).mockRejectedValueOnce(updateError);

      const messageWithInspirations = {
        ...validMessage,
        inspiration_ids: ['insp1'],
      };
      await handler.handle(mockWebSocketClient, messageWithInspirations);

      expect(
        mockSessionsService.updateSessionInspirations,
      ).toHaveBeenCalledTimes(1);
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Failed to update inspiration IDs'),
      );
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
    });

    it('should send error if session is not found', async () => {
      (mockSessionsService.getSessionById as jest.Mock).mockRejectedValueOnce(
        new NotFoundException(),
      );
      await handler.handle(mockWebSocketClient, validMessage);
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        expect.any(String),
        StandardErrorCode.SESSION_NOT_FOUND,
        validMessage.session_id,
        validMessage.request_id,
      );
      expect(mockBackendWsService.sendChatRequest).not.toHaveBeenCalled();
    });

    // --- New Tests for Language Handling ---

    it('should call updateSessionMetadata if language is provided and different from current', async () => {
      const messageWithLang = createMessage('kql' as SupportedLanguage);
      const initialMeta = createSessionMeta(SupportedLanguage.SIGMA); // Different language
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(mockWebSocketClient, messageWithLang);

      expect(mockSessionsService.getSessionById).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
      );
      expect(mockSessionsService.updateSessionMetadata).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
        {
          language: 'kql',
          timezone: undefined,
        },
      );
      expect(mockBackendWsService.sendSettingsRequest).toHaveBeenCalledWith(
        mockClientData.sessionId,
        mockClientData.userId,
        {
          type: 'SET_SETTINGS',
          session_id: mockClientData.sessionId,
          request_id: messageWithLang.request_id,
          settings: {
            detection_language: 'kql',
            timezone: undefined,
          },
        },
      );
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    it('should NOT call updateSessionMetadata but proceed if language is provided and matches current language', async () => {
      const messageWithLang = createMessage('kql' as SupportedLanguage);
      const initialMeta = createSessionMeta(SupportedLanguage.KQL); // Same language as message
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(mockWebSocketClient, messageWithLang);

      expect(mockSessionsService.getSessionById).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
      );
      // Important: Should not call updateSessionMetadata if language is the same
      expect(mockSessionsService.updateSessionMetadata).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendSettingsRequest).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1); // Should still proceed
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    it('should proceed if language is NOT provided but session has a language set', async () => {
      const messageWithoutLang = createMessage(undefined); // Pass undefined instead of null
      const initialMeta = createSessionMeta(SupportedLanguage.SPL); // Has language (spl)
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(mockWebSocketClient, messageWithoutLang);

      expect(mockSessionsService.getSessionById).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
      );
      expect(mockSessionsService.updateSessionMetadata).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    it('should send LANGUAGE_NOT_SET error if language is NOT provided and session has no language', async () => {
      const messageWithoutLang = createMessage(undefined); // Pass undefined instead of null
      const initialMeta = createSessionMeta(null); // No language set yet
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(mockWebSocketClient, messageWithoutLang);

      expect(mockSessionsService.getSessionById).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
      );
      expect(mockSessionsService.updateSessionMetadata).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendChatRequest).not.toHaveBeenCalled();
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        expect.stringContaining('Language not set'),
        StandardErrorCode.LANGUAGE_NOT_SET,
        mockClientData.sessionId,
        messageWithoutLang.request_id,
      );
    });

    it('should send SESSION_NOT_FOUND error if getSessionById throws NotFoundException', async () => {
      const messageWithLang = createMessage('kql' as SupportedLanguage);
      (mockSessionsService.getSessionById as jest.Mock).mockRejectedValue(
        new NotFoundException('Session not there'),
      );

      await handler.handle(mockWebSocketClient, messageWithLang);

      expect(mockSessionsService.getSessionById).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
      );
      expect(mockSessionsService.updateSessionMetadata).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendChatRequest).not.toHaveBeenCalled();
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        'Session not found',
        StandardErrorCode.SESSION_NOT_FOUND,
        mockClientData.sessionId,
        messageWithLang.request_id,
      );
    });

    it('should send INTERNAL_SERVER_ERROR if updateSessionMetadata fails', async () => {
      const messageWithLang = createMessage('kql' as SupportedLanguage);
      const initialMeta = createSessionMeta(SupportedLanguage.SIGMA); // Different language
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );
      (
        mockSessionsService.updateSessionMetadata as jest.Mock
      ).mockRejectedValue(new InternalServerErrorException('DB update failed'));

      await handler.handle(mockWebSocketClient, messageWithLang);

      expect(mockSessionsService.getSessionById).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
      );
      expect(mockSessionsService.updateSessionMetadata).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
        {
          language: 'kql',
          timezone: undefined,
        },
      );
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        'Failed to update session metadata',
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        mockClientData.sessionId,
        messageWithLang.request_id,
      );
    });

    // --- End New Tests for Language Handling ---

    // --- Tests for Timezone Handling ---

    it('should update timezone when timezone changes', async () => {
      const messageWithTimezone = createMessage(
        undefined,
        undefined,
        undefined,
        'Test prompt',
        'America/New_York',
      );
      const initialMeta = createSessionMeta(SupportedLanguage.SIGMA, 'UTC'); // Different timezone
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(mockWebSocketClient, messageWithTimezone);

      expect(mockSessionsService.updateSessionMetadata).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
        {
          language: undefined,
          timezone: 'America/New_York',
        },
      );
      expect(mockBackendWsService.sendSettingsRequest).toHaveBeenCalledWith(
        mockClientData.sessionId,
        mockClientData.userId,
        {
          type: 'SET_SETTINGS',
          session_id: mockClientData.sessionId,
          request_id: messageWithTimezone.request_id,
          settings: {
            detection_language: undefined,
            timezone: 'America/New_York',
          },
        },
      );
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    it('should update both language and timezone when both change', async () => {
      const messageWithBoth = createMessage(
        'kql' as SupportedLanguage,
        undefined,
        undefined,
        'Test prompt',
        'America/New_York',
      );
      const initialMeta = createSessionMeta(SupportedLanguage.SIGMA, 'UTC'); // Both different
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(mockWebSocketClient, messageWithBoth);

      expect(mockSessionsService.updateSessionMetadata).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
        {
          language: 'kql',
          timezone: 'America/New_York',
        },
      );
      expect(mockBackendWsService.sendSettingsRequest).toHaveBeenCalledWith(
        mockClientData.sessionId,
        mockClientData.userId,
        {
          type: 'SET_SETTINGS',
          session_id: mockClientData.sessionId,
          request_id: messageWithBoth.request_id,
          settings: {
            detection_language: 'kql',
            timezone: 'America/New_York',
          },
        },
      );
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    it('should NOT update timezone when timezone matches current timezone', async () => {
      const messageWithSameTimezone = createMessage(
        undefined,
        undefined,
        undefined,
        'Test prompt',
        'America/New_York',
      );
      const initialMeta = createSessionMeta(
        SupportedLanguage.SIGMA,
        'America/New_York',
      ); // Same timezone
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(mockWebSocketClient, messageWithSameTimezone);

      expect(mockSessionsService.updateSessionMetadata).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendSettingsRequest).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    it('should handle timezone update failures gracefully', async () => {
      const messageWithTimezone = createMessage(
        undefined,
        undefined,
        undefined,
        'Test prompt',
        'America/New_York',
      );
      const initialMeta = createSessionMeta(SupportedLanguage.SIGMA, 'UTC'); // Different timezone
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );
      (
        mockSessionsService.updateSessionMetadata as jest.Mock
      ).mockRejectedValue(new Error('Database update failed'));

      await handler.handle(mockWebSocketClient, messageWithTimezone);

      expect(mockSessionsService.updateSessionMetadata).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
        {
          language: undefined,
          timezone: 'America/New_York',
        },
      );
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        'Failed to update session metadata',
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        mockClientData.sessionId,
        messageWithTimezone.request_id,
      );
    });

    it('should handle sendSettingsRequest failures gracefully', async () => {
      const messageWithTimezone = createMessage(
        undefined,
        undefined,
        undefined,
        'Test prompt',
        'America/New_York',
      );
      const initialMeta = createSessionMeta(SupportedLanguage.SIGMA, 'UTC'); // Different timezone
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );
      (
        mockBackendWsService.sendSettingsRequest as jest.Mock
      ).mockImplementation(() => {
        throw new Error('Backend settings request failed');
      });

      await handler.handle(mockWebSocketClient, messageWithTimezone);

      expect(mockBackendWsService.sendSettingsRequest).toHaveBeenCalledWith(
        mockClientData.sessionId,
        mockClientData.userId,
        {
          type: 'SET_SETTINGS',
          session_id: mockClientData.sessionId,
          request_id: messageWithTimezone.request_id,
          settings: {
            detection_language: undefined,
            timezone: 'America/New_York',
          },
        },
      );
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        'Failed to update session settings',
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        mockClientData.sessionId,
        messageWithTimezone.request_id,
      );
    });

    // --- End Tests for Timezone Handling ---

    // --- Enhanced Language Change Detection Tests ---

    it('should extract language from artifacts when detection_rule has no content', async () => {
      const messageWithEmptyDetectionRule = createMessage(undefined, {
        detection_rule: {
          type: 'spl' as SupportedLanguage,
          content: undefined, // No content triggers language extraction
        },
      });
      const initialMeta = createSessionMeta(SupportedLanguage.SIGMA);
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(mockWebSocketClient, messageWithEmptyDetectionRule);

      // Should have extracted language from artifacts and updated
      expect(mockSessionsService.updateSessionMetadata).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
        {
          language: 'spl',
          timezone: undefined,
        },
      );
      expect(mockBackendWsService.sendSettingsRequest).toHaveBeenCalledWith(
        mockClientData.sessionId,
        mockClientData.userId,
        expect.objectContaining({
          settings: {
            detection_language: 'spl',
            timezone: undefined,
          },
        }),
      );
    });

    it('should not extract language from artifacts when detection_rule has content', async () => {
      const messageWithDetectionRule = createMessage(undefined, {
        detection_rule: {
          type: 'spl' as SupportedLanguage,
          content: 'some detection rule content', // Has content, should not extract
        },
      });
      const initialMeta = createSessionMeta(SupportedLanguage.SIGMA);
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(mockWebSocketClient, messageWithDetectionRule);

      // Should not have extracted language from artifacts
      expect(mockSessionsService.updateSessionMetadata).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendSettingsRequest).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
    });

    it('should prefer explicit language over artifacts language', async () => {
      const messageWithBoth = createMessage(
        'kql' as SupportedLanguage, // Explicit language
        {
          detection_rule: {
            type: 'spl' as SupportedLanguage, // Artifacts language
            content: undefined,
          },
        },
      );
      const initialMeta = createSessionMeta(SupportedLanguage.SIGMA);
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(mockWebSocketClient, messageWithBoth);

      // Should use explicit language (kql), not artifacts language (spl)
      expect(mockSessionsService.updateSessionMetadata).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
        {
          language: 'kql',
          timezone: undefined,
        },
      );
      expect(mockBackendWsService.sendSettingsRequest).toHaveBeenCalledWith(
        mockClientData.sessionId,
        mockClientData.userId,
        expect.objectContaining({
          settings: {
            detection_language: 'kql',
            timezone: undefined,
          },
        }),
      );
    });

    it('should handle artifacts language extraction combined with timezone', async () => {
      const messageWithArtifactsAndTimezone = createMessage(
        undefined,
        {
          detection_rule: {
            type: 'spl' as SupportedLanguage,
            content: undefined,
          },
        },
        undefined,
        'Test prompt',
        'America/Chicago',
      );
      const initialMeta = createSessionMeta(SupportedLanguage.SIGMA, 'UTC');
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(
        mockWebSocketClient,
        messageWithArtifactsAndTimezone,
      );

      // Should update both language from artifacts and timezone
      expect(mockSessionsService.updateSessionMetadata).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
        {
          language: 'spl',
          timezone: 'America/Chicago',
        },
      );
      expect(mockBackendWsService.sendSettingsRequest).toHaveBeenCalledWith(
        mockClientData.sessionId,
        mockClientData.userId,
        expect.objectContaining({
          settings: {
            detection_language: 'spl',
            timezone: 'America/Chicago',
          },
        }),
      );
    });

    it('should not update when both language and timezone match current values', async () => {
      const messageWithSameValues = createMessage(
        'sigma' as SupportedLanguage,
        undefined,
        undefined,
        'Test prompt',
        'America/New_York',
      );
      const initialMeta = createSessionMeta(
        SupportedLanguage.SIGMA,
        'America/New_York',
      ); // Same values
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(mockWebSocketClient, messageWithSameValues);

      // Should not update anything since values are the same
      expect(mockSessionsService.updateSessionMetadata).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendSettingsRequest).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    // --- End Enhanced Language Change Detection Tests ---

    // --- Comprehensive Error Handling Tests ---

    it('should handle getSessionById non-NotFoundException errors', async () => {
      const messageWithLang = createMessage('kql' as SupportedLanguage);
      (mockSessionsService.getSessionById as jest.Mock).mockRejectedValue(
        new Error('Database connection failed'),
      );

      await handler.handle(mockWebSocketClient, messageWithLang);

      expect(mockSessionsService.getSessionById).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
      );
      expect(mockSessionsService.updateSessionMetadata).not.toHaveBeenCalled();
      expect(mockBackendWsService.sendChatRequest).not.toHaveBeenCalled();
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        'Error processing session data',
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        mockClientData.sessionId,
        messageWithLang.request_id,
      );
    });

    it('should handle updateSessionMetadata failure when updating both language and timezone', async () => {
      const messageWithBoth = createMessage(
        'kql' as SupportedLanguage,
        undefined,
        undefined,
        'Test prompt',
        'America/New_York',
      );
      const initialMeta = createSessionMeta(SupportedLanguage.SIGMA, 'UTC');
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );
      (
        mockSessionsService.updateSessionMetadata as jest.Mock
      ).mockRejectedValue(new Error('Database constraint violation'));

      await handler.handle(mockWebSocketClient, messageWithBoth);

      expect(mockSessionsService.updateSessionMetadata).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
        {
          language: 'kql',
          timezone: 'America/New_York',
        },
      );
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        'Failed to update session metadata',
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        mockClientData.sessionId,
        messageWithBoth.request_id,
      );
    });

    it('should handle sendSettingsRequest failure but still continue to updateSessionMetadata', async () => {
      const messageWithLang = createMessage('kql' as SupportedLanguage);
      const initialMeta = createSessionMeta(SupportedLanguage.SIGMA);
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );
      (
        mockBackendWsService.sendSettingsRequest as jest.Mock
      ).mockImplementation(() => {
        throw new Error('Backend connection timeout');
      });

      await handler.handle(mockWebSocketClient, messageWithLang);

      expect(mockBackendWsService.sendSettingsRequest).toHaveBeenCalledWith(
        mockClientData.sessionId,
        mockClientData.userId,
        expect.objectContaining({
          settings: {
            detection_language: 'kql',
            timezone: undefined,
          },
        }),
      );
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        'Failed to update session settings',
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        mockClientData.sessionId,
        messageWithLang.request_id,
      );
    });

    it('should handle updateSessionInspirations failure but continue processing', async () => {
      const messageWithInspirations = createMessage(undefined, undefined, [
        'insp1',
        'insp2',
      ]);
      const loggerErrorSpy = jest.spyOn(handler['logger'], 'error');
      (
        mockSessionsService.updateSessionInspirations as jest.Mock
      ).mockRejectedValue(new Error('Inspiration update failed'));

      await handler.handle(mockWebSocketClient, messageWithInspirations);

      expect(
        mockSessionsService.updateSessionInspirations,
      ).toHaveBeenCalledWith(mockClientData.userId, mockClientData.sessionId, [
        'insp1',
        'insp2',
      ]);
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Failed to update inspiration IDs'),
      );
      // Should continue processing despite inspiration update failure
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    it('should handle backend sendChatRequest failure', async () => {
      const validMessage = createMessage();
      (mockBackendWsService.sendChatRequest as jest.Mock).mockImplementation(
        () => {
          throw new Error('Backend service unavailable');
        },
      );

      await handler.handle(mockWebSocketClient, validMessage);

      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledWith(
        mockClientData.sessionId,
        mockClientData.userId,
        validMessage,
      );
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        'Failed to process request',
        StandardErrorCode.BACKEND_UNAVAILABLE,
        mockClientData.sessionId,
        validMessage.request_id,
      );
    });

    // --- End Comprehensive Error Handling Tests ---

    it('should call sendSessionNameRequest when generate_session_name is true', async () => {
      const messageWithNameGen = {
        ...validMessage,
        generate_session_name: true,
      };

      await handler.handle(mockWebSocketClient, messageWithNameGen);

      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockBackendWsService.sendSessionNameRequest).toHaveBeenCalledWith(
        mockClientData.sessionId,
        mockClientData.userId,
        {
          type: 'GENERATE_SESSION_NAME',
          session_id: mockClientData.sessionId,
          request_id: messageWithNameGen.request_id,
        },
      );
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    it('should NOT call sendSessionNameRequest when generate_session_name is false or undefined', async () => {
      const messageWithoutNameGen = {
        ...validMessage,
        generate_session_name: false,
      };

      await handler.handle(mockWebSocketClient, messageWithoutNameGen);

      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(
        mockBackendWsService.sendSessionNameRequest,
      ).not.toHaveBeenCalled();
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    it('should proceed with chat request even if sendSessionNameRequest fails', async () => {
      const loggerErrorSpy = jest.spyOn(handler['logger'], 'error');
      const nameGenError = new Error('Name generation failed');
      (
        mockBackendWsService.sendSessionNameRequest as jest.Mock
      ).mockImplementation(() => {
        throw nameGenError;
      });

      const messageWithNameGen = {
        ...validMessage,
        generate_session_name: true,
      };

      await handler.handle(mockWebSocketClient, messageWithNameGen);

      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockBackendWsService.sendSessionNameRequest).toHaveBeenCalledTimes(
        1,
      );
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          'Failed to send session name generation request',
        ),
      );
      // Should not fail the chat request
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    it('should handle detection_rule artifacts and set language from type', async () => {
      const messageWithDetectionRule = createMessage(undefined, {
        detection_rule: {
          type: 'spl' as SupportedLanguage,
          // content is undefined, which should trigger the artifact handling
        },
      });
      const initialMeta = createSessionMeta(null); // No language set
      (mockSessionsService.getSessionById as jest.Mock).mockResolvedValue(
        initialMeta,
      );

      await handler.handle(mockWebSocketClient, messageWithDetectionRule);

      expect(mockSessionsService.updateSessionMetadata).toHaveBeenCalledWith(
        mockClientData.userId,
        mockClientData.sessionId,
        {
          language: 'spl',
          timezone: undefined,
        },
      );
      expect(mockBackendWsService.sendSettingsRequest).toHaveBeenCalledWith(
        mockClientData.sessionId,
        mockClientData.userId,
        expect.objectContaining({
          settings: {
            detection_language: 'spl',
            timezone: undefined,
          },
        }),
      );
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
    });

    // --- End New Tests for Language Handling ---

    it('should forward the request to the backend service if all checks pass', async () => {
      await handler.handle(mockWebSocketClient, validMessage);
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledTimes(1);
      expect(mockBackendWsService.sendChatRequest).toHaveBeenCalledWith(
        mockClientData.sessionId,
        mockClientData.userId,
        validMessage, // The validated & potentially augmented message DTO
      );
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    it('should send error if backend service fails', async () => {
      const backendError = new Error('Connection failed');
      (mockBackendWsService.sendChatRequest as jest.Mock).mockImplementation(
        () => {
          throw backendError;
        },
      );
      await handler.handle(mockWebSocketClient, validMessage);
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        expect.anything(),
        expect.any(String),
        StandardErrorCode.BACKEND_UNAVAILABLE,
        validMessage.session_id,
        validMessage.request_id,
      );
    });
  });
});
