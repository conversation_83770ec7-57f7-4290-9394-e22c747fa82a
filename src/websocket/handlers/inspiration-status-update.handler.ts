import { Injectable, Logger } from '@nestjs/common';
import { IBackendMessageHandler } from './backend-message-handler.interface';
import {
  InspirationStatusUpdateDto,
  UiOutputMessage,
} from '../dto/ui-output-message.dto';
import { BackendInspirationsMessage } from '../dto/backend-message.dto';
import {
  BackendMessageType,
  UiOutputMessageType,
} from '../constants/message-types.enum';

@Injectable()
export class InspirationStatusUpdateHandler implements IBackendMessageHandler {
  private readonly logger = new Logger(InspirationStatusUpdateHandler.name);

  canHandle(message: Record<string, unknown>): boolean {
    // Check for type or specific fields indicative of INSPIRATION_STATUS_UPDATE
    return (
      message.type === BackendMessageType.InspirationsUpdate ||
      ('inspirations' in message && Array.isArray(message.inspirations))
    );
  }

  // This method doesn't use await but is marked as returning a Promise to conform to the interface
  transform(
    sessionId: string,
    message: Record<string, unknown>,
  ): Promise<UiOutputMessage | null> {
    this.logger.debug(`[${sessionId}] Handling S2C_INSPIRATIONS`);
    const backendStatus = message as Partial<BackendInspirationsMessage>;
    const requestId =
      typeof message.request_id === 'string' ? message.request_id : undefined;

    const uiStatus: InspirationStatusUpdateDto = {
      type: UiOutputMessageType.InspirationStatusUpdate,
      session_id: sessionId,
      request_id: requestId,
      timestamp: backendStatus.timestamp,
      inspirations: backendStatus.payload?.inspirations ?? [],
    };

    return Promise.resolve(uiStatus);
  }
}
