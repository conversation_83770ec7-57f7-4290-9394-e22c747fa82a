import {
  Injectable,
  Logger,
  ValidationPipe,
  NotFoundException,
} from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { MessageHandler } from '../interfaces/message-handler.interface';
import { FeedbackRequestDto } from '../dto/feedback-request.dto';
import { ClientManager, WebSocketWithId } from '../managers/client.manager';
import { ErrorHandlerService } from '../services/error-handler.service';
import { BackendWsService } from '../services/backend-ws.service';
import { SessionsService } from '../../sessions/sessions.service';
import { StandardErrorCode } from '../dto/common.dto';
import { UiInputMessageType } from '../constants/message-types.enum';

@Injectable()
export class FeedbackRequestHandler
  implements MessageHandler<FeedbackRequestDto>
{
  private readonly logger = new Logger(FeedbackRequestHandler.name);
  private readonly validationPipe: ValidationPipe;

  constructor(
    private readonly clientManager: ClientManager,
    private readonly errorHandlerService: ErrorHandlerService,
    private readonly backendWsService: BackendWsService,
    private readonly sessionsService: SessionsService,
  ) {
    this.validationPipe = new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    });
  }

  canHandle(message: Record<string, unknown>): boolean {
    return message.type === UiInputMessageType.FeedbackRequest;
  }

  async validate(
    message: Record<string, unknown>,
  ): Promise<FeedbackRequestDto> {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const validatedDto = await this.validationPipe.transform(message, {
      type: 'body',
      metatype: FeedbackRequestDto,
    });
    return validatedDto as FeedbackRequestDto;
  }

  async handle(
    client: WebSocketWithId,
    message: FeedbackRequestDto,
  ): Promise<void> {
    const clientId = client.clientId;
    const clientData = this.clientManager.getClientData(clientId);

    if (
      !clientData ||
      clientData.status !== 'ready' ||
      !clientData.authenticated
    ) {
      this.logger.warn(
        `[${clientId}] FeedbackRequestHandler received request from unready/unauth client.`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Client not ready or not authenticated',
        StandardErrorCode.UNAUTHORIZED,
        message.session_id,
        message.request_id,
      );
      return;
    }

    const sessionId = message.session_id;
    const requestId = message.request_id || uuidv4();
    message.request_id = requestId;

    if (sessionId !== clientData.sessionId) {
      this.logger.error(
        `[${clientId}] Session ID mismatch in FeedbackRequestHandler! Client data: ${clientData.sessionId}, message: ${sessionId}`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Session ID mismatch',
        StandardErrorCode.INVALID_REQUEST,
        clientData.sessionId,
        requestId,
      );
      return;
    }

    // Verify session exists
    try {
      await this.sessionsService.getSessionById(clientData.userId, sessionId);
    } catch (error) {
      if (error instanceof NotFoundException) {
        this.logger.error(`[${clientId}] Session not found for ${sessionId}`);
        this.errorHandlerService.sendErrorToClient(
          client,
          'Session not found',
          StandardErrorCode.SESSION_NOT_FOUND,
          sessionId,
          requestId,
        );
        return;
      }
      this.logger.error(
        `[${clientId}] Error retrieving session ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Error processing session data',
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        sessionId,
        requestId,
      );
      return;
    }

    this.logger.log(
      `[${clientId}] Forwarding SEND_FEEDBACK (ReqID: ${requestId}) for session ${sessionId} to backend. Chat ID: ${message.payload.chat_id}, Like: ${message.payload.like}`,
    );
    try {
      this.backendWsService.sendFeedbackRequest(
        sessionId,
        clientData.userId,
        message,
      );
    } catch (error) {
      this.logger.error(
        `[${clientId}] Failed to send SEND_FEEDBACK to backend: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Failed to process request',
        StandardErrorCode.BACKEND_UNAVAILABLE,
        sessionId,
        requestId,
      );
    }
  }
}
