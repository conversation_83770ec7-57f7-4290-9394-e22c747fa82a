import {
  Injectable,
  Logger,
  ValidationPipe,
  NotFoundException,
} from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { MessageHandler } from '../interfaces/message-handler.interface';
import { ChatRequestDto } from '../dto/chat-request.dto';
import { ClientManager, WebSocketWithId } from '../managers/client.manager';
import { ErrorHandlerService } from '../services/error-handler.service';
import { BackendWsService } from '../services/backend-ws.service';
import { SessionsService } from '../../sessions/sessions.service';
import { StandardErrorCode } from '../dto/common.dto';
import { UiInputMessageType } from '../constants/message-types.enum';
import { SupportedLanguage } from '../../common/languages';
import { SessionMetadataDto } from '../../sessions/dto/session-metadata.dto';

@Injectable()
export class ChatRequestHandler implements MessageHandler<ChatRequestDto> {
  private readonly logger = new Logger(ChatRequestHandler.name);
  private readonly validationPipe: ValidationPipe;

  constructor(
    private readonly clientManager: ClientManager,
    private readonly errorHandlerService: ErrorHandlerService,
    private readonly backendWsService: BackendWsService,
    private readonly sessionsService: SessionsService,
  ) {
    this.validationPipe = new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    });
  }

  canHandle(message: Record<string, unknown>): boolean {
    return message.type === UiInputMessageType.ChatRequest;
  }

  async validate(message: Record<string, unknown>): Promise<ChatRequestDto> {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const validatedDto = await this.validationPipe.transform(message, {
      type: 'body',
      metatype: ChatRequestDto,
    });
    return validatedDto as ChatRequestDto;
  }

  async handle(
    client: WebSocketWithId,
    message: ChatRequestDto,
  ): Promise<void> {
    const clientId = client.clientId;
    const clientData = this.clientManager.getClientData(clientId);

    if (
      !clientData ||
      clientData.status !== 'ready' ||
      !clientData.authenticated
    ) {
      this.logger.warn(
        `[${clientId}] ChatRequestHandler received request from unready/unauth client.`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Client not ready or not authenticated',
        StandardErrorCode.UNAUTHORIZED,
        message.session_id,
        message.request_id,
      );
      return;
    }

    const sessionId = message.session_id;
    const requestId = message.request_id || uuidv4();
    message.request_id = requestId;

    if (sessionId !== clientData.sessionId) {
      this.logger.error(
        `[${clientId}] Session ID mismatch in ChatRequestHandler! Client data: ${clientData.sessionId}, message: ${sessionId}`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Session ID mismatch',
        StandardErrorCode.INVALID_REQUEST,
        clientData.sessionId,
        requestId,
      );
      return;
    }

    // --- Settings Update Handling ---
    if (
      message.artifacts?.detection_rule &&
      !message.artifacts?.detection_rule.content
    ) {
      if (!message.language && message.artifacts.detection_rule.type) {
        message.language = message.artifacts.detection_rule.type;
      }
      message.artifacts.detection_rule = undefined;
    }

    let sessionMetadata: SessionMetadataDto | null = null;

    try {
      // Fetch session metadata early to check language status
      sessionMetadata = await this.sessionsService.getSessionById(
        clientData.userId,
        sessionId,
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        this.logger.error(`[${clientId}] Session not found for ${sessionId}`);
        this.errorHandlerService.sendErrorToClient(
          client,
          'Session not found',
          StandardErrorCode.SESSION_NOT_FOUND,
          sessionId,
          requestId,
        );
        return;
      }
      // Handle potential errors from setSessionLanguageAndLock or getSessionById
      this.logger.error(
        `[${clientId}] Error during session/language processing: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Error processing session data',
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        sessionId,
        requestId,
      );
      return;
    }

    // Check for settings updates
    let newDetectionLanguage: SupportedLanguage | null = null;
    let newTimezone: string | null = null;

    // If a language is provided in the request, process it
    if (message.language && message.language !== sessionMetadata.language) {
      const requestedLanguage = message.language;
      this.logger.debug(
        `[${sessionId}] CHAT_REQUEST received with language: ${requestedLanguage}. current language: ${sessionMetadata.language}`,
      );
      // If not locked, or locked to the same language (idempotent), set/lock it
      this.logger.log(
        `[${sessionId}] Setting language to ${requestedLanguage} and locking session.`,
      );
      newDetectionLanguage = requestedLanguage;
    } else {
      // If no language is provided in the request, ensure one is set for the session
      if (!sessionMetadata.language) {
        this.logger.warn(
          `[${clientId}] Language not set for session ${sessionId} and not provided in chat request.`,
        );
        this.errorHandlerService.sendErrorToClient(
          client,
          'Language not set for session. Please set a language first.',
          StandardErrorCode.LANGUAGE_NOT_SET,
          sessionId,
          requestId,
        );
        return;
      }
      this.logger.debug(
        `[${clientId}] No language in request, using existing session language: ${sessionMetadata.language}`,
      );
    }

    // Check for timezone updates
    if (message.timezone && message.timezone !== sessionMetadata.timezone) {
      this.logger.debug(
        `[${sessionId}] CHAT_REQUEST received with timezone: ${message.timezone}. current timezone: ${sessionMetadata.timezone}`,
      );
      newTimezone = message.timezone;
    }

    // Send SET_SETTINGS request to backend if there are any updates
    if (newDetectionLanguage || newTimezone) {
      try {
        this.logger.debug(
          `[${sessionId}] Sending SET_SETTINGS request with detection language: ${newDetectionLanguage} and timezone: ${newTimezone}`,
        );
        this.backendWsService.sendSettingsRequest(
          sessionId,
          clientData.userId,
          {
            type: 'SET_SETTINGS',
            session_id: sessionId,
            request_id: requestId,
            settings: {
              detection_language: newDetectionLanguage ?? undefined,
              timezone: newTimezone ?? undefined,
            },
          },
        );
      } catch (error) {
        this.logger.error(
          `[${clientId}] Failed to send SET_SETTINGS request: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        this.errorHandlerService.sendErrorToClient(
          client,
          'Failed to update session settings',
          StandardErrorCode.INTERNAL_SERVER_ERROR,
          sessionId,
          requestId,
        );
      }

      // Update session metadata in DynamoDB
      try {
        await this.sessionsService.updateSessionMetadata(
          clientData.userId,
          sessionId,
          {
            language: newDetectionLanguage ?? undefined,
            timezone: newTimezone ?? undefined,
          },
        );
      } catch (error) {
        this.logger.error(
          `[${clientId}] Failed to update session metadata: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        this.errorHandlerService.sendErrorToClient(
          client,
          'Failed to update session metadata',
          StandardErrorCode.INTERNAL_SERVER_ERROR,
          sessionId,
          requestId,
        );
      }
    }
    // --- End Settings Update Handling ---

    // --- Update Inspiration IDs ---
    if (message.inspiration_ids && Array.isArray(message.inspiration_ids)) {
      try {
        this.logger.debug(
          `[${sessionId}] Updating inspiration IDs from CHAT_REQUEST.`,
        );
        await this.sessionsService.updateSessionInspirations(
          clientData.userId, // userId from clientData
          sessionId,
          message.inspiration_ids,
        );
      } catch (error) {
        // Log error but continue processing the chat request
        this.logger.error(
          `[${clientId}] Failed to update inspiration IDs for session ${sessionId}: ${error instanceof Error ? error.message : String(error)}`,
        );
        // Optional: Send a non-critical error/warning to client?
        // this.errorHandlerService.sendErrorToClient(...);
      }
    }
    // --- End Update Inspiration IDs ---

    this.logger.log(
      `[${clientId}] Forwarding CHAT_REQUEST (ReqID: ${requestId}) for session ${sessionId} to backend.`,
    );
    try {
      this.backendWsService.sendChatRequest(
        sessionId,
        clientData.userId,
        message,
      );
    } catch (error) {
      this.logger.error(
        `[${clientId}] Failed to send CHAT_REQUEST to backend: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Failed to process request',
        StandardErrorCode.BACKEND_UNAVAILABLE,
        sessionId,
        requestId,
      );
    }

    if (message.generate_session_name) {
      try {
        this.backendWsService.sendSessionNameRequest(
          sessionId,
          clientData.userId,
          {
            type: 'GENERATE_SESSION_NAME',
            session_id: sessionId,
            request_id: requestId,
          },
        );
      } catch (error) {
        this.logger.error(
          `[${clientId}] Failed to send session name generation request: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        // Don't fail the chat request if session name generation fails
      }
    }
  }
}
