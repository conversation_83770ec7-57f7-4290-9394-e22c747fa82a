import { Injectable, Logger } from '@nestjs/common';
import { IBackendMessageHandler } from './backend-message-handler.interface';
import { UiOutputMessage } from '../dto/ui-output-message.dto';
import { SessionsService } from '../../sessions/sessions.service';
import { ClientManager } from '../managers/client.manager';
import { BackendSettingsMessage } from '../dto/backend-message.dto';
import { SettingsUpdateDto } from '../dto/settings-update.dto';
import {
  BackendMessageType,
  UiOutputMessageType,
} from '../constants/message-types.enum';

@Injectable()
export class SettingsUpdateHandler implements IBackendMessageHandler {
  private readonly logger = new Logger(SettingsUpdateHandler.name);

  constructor(
    private readonly sessionsService: SessionsService,
    private readonly clientManager: ClientManager,
  ) {}

  canHandle(message: Record<string, unknown>): boolean {
    // Check for type or specific fields indicative of SETTINGS_UPDATE
    return (
      message.type === BackendMessageType.SettingsUpdate ||
      ('payload' in message &&
        typeof message.payload === 'object' &&
        message.payload !== null &&
        'success' in message.payload)
    );
  }

  transform(
    sessionId: string,
    message: Record<string, unknown>,
  ): Promise<UiOutputMessage | null> {
    this.logger.debug(`[${sessionId}] Handling S2C_SETTINGS`);
    const backendUpdate = message as Partial<BackendSettingsMessage>;

    const uiUpdate: SettingsUpdateDto = {
      type: UiOutputMessageType.SettingsUpdate,
      session_id: sessionId,
      request_id: backendUpdate.request_id,
      timestamp: backendUpdate.timestamp,
      payload: {
        success: backendUpdate.payload?.success ?? false,
      },
      error: backendUpdate.error,
    };

    return Promise.resolve(uiUpdate);
  }
}
