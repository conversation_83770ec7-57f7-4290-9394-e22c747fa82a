import { Injectable, Logger } from '@nestjs/common';
import { IBackendMessageHandler } from './backend-message-handler.interface';
import { UiOutputMessage } from '../dto/ui-output-message.dto';
import { SessionsService } from '../../sessions/sessions.service';
import { ClientManager } from '../managers/client.manager';
import { BackendSessionNameMessage } from '../dto/backend-message.dto';
import {
  BackendMessageType,
  UiOutputMessageType,
} from '../constants/message-types.enum';
import { ChatUpdateDto } from '../dto/session-message.dto';

@Injectable()
export class SessionNameUpdateHandler implements IBackendMessageHandler {
  private readonly logger = new Logger(SessionNameUpdateHandler.name);

  constructor(
    private readonly sessionsService: SessionsService,
    private readonly clientManager: ClientManager,
  ) {}

  canHandle(message: Record<string, unknown>): boolean {
    // Check for type or specific fields indicative of SESSION_NAME_UPDATE
    return (
      message.type === BackendMessageType.SessionNameUpdate ||
      ('payload' in message &&
        typeof message.payload === 'object' &&
        message.payload !== null &&
        'session_name' in message.payload)
    );
  }

  transform(
    sessionId: string,
    message: Record<string, unknown>,
  ): Promise<UiOutputMessage | null> {
    this.logger.debug(`[${sessionId}] Handling S2C_SESSION_NAME`);
    const backendUpdate = message as Partial<BackendSessionNameMessage>;
    const userId = this.clientManager.getUserIdForSession(sessionId);
    const sessionName = backendUpdate.payload?.session_name;

    if (!userId || !sessionName) {
      this.logger.error(
        `[${sessionId}] Invalid session name update: sessionName=${sessionName} userId=${userId}`,
      );
      return Promise.resolve(null);
    }

    const uiUpdate: ChatUpdateDto = {
      type: UiOutputMessageType.ChatUpdate,
      session_id: sessionId,
      request_id: backendUpdate.request_id,
      timestamp: backendUpdate.timestamp,
      responses: [],
      generated_session_name: backendUpdate.payload?.session_name,
      error: backendUpdate.error,
    };

    void this.sessionsService
      .updateSessionTitle(userId, sessionId, sessionName, true)
      .then(() => {
        this.logger.debug(
          `[${sessionId}] Session name updated: ${sessionName}`,
        );
      })
      .catch((error) => {
        this.logger.error(
          `[${sessionId}] Error updating session name: ${error}`,
        );
      });

    return Promise.resolve(uiUpdate);
  }
}
