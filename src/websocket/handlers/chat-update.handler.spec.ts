import { Test, TestingModule } from '@nestjs/testing';
import { ChatUpdateHandler } from './chat-update.handler';
import { SessionsService } from '../../sessions/sessions.service';
import { ClientManager } from '../managers/client.manager';
import { SessionManager } from '../managers/session.manager';
import {
  BackendChatMessage,
  BackendChatResponse,
} from '../dto/backend-message.dto';
import { ChatUpdateDto } from '../dto/session-message.dto';
import {
  BackendMessageType,
  UiOutputMessageType,
} from '../constants/message-types.enum';
import { DetectionRule } from '../dto/common.dto';
import { SupportedLanguage } from '../../common/languages';

describe('ChatUpdateHandler', () => {
  let handler: ChatUpdateHandler;
  let mockSessionsService: Partial<SessionsService>;
  let mockClientManager: Partial<ClientManager>;
  let mockSessionManager: Partial<SessionManager>;

  const sessionId = 'test-session-123';
  const requestId = 'test-request-456';
  const timestamp = '2024-01-15T10:30:00Z';

  // Helper function to create a BackendChatMessage
  const createBackendChatMessage = (
    overrides: Partial<BackendChatMessage> = {},
  ): BackendChatMessage => ({
    type: BackendMessageType.ChatUpdate,
    timestamp: timestamp,
    request_id: requestId,
    payload: {
      status: 'processing',
      responses: [],
      done: false,
    },
    ...overrides,
  });

  // Helper function to create a BackendChatResponse
  const createBackendChatResponse = (
    overrides: Partial<BackendChatResponse> = {},
  ): BackendChatResponse => ({
    id: 'response-1',
    type: 'text',
    content: 'Test response content',
    status: 'completed',
    done: false,
    ...overrides,
  });

  // Helper function to create a DetectionRule
  const createDetectionRule = (
    overrides: Partial<DetectionRule> = {},
  ): DetectionRule => ({
    id: 'rule-1',
    type: SupportedLanguage.SIGMA,
    title: 'Test Detection Rule',
    content: 'title: Test Rule\ndetection:\n  condition: test',
    status: 'completed',
    done: true,
    ...overrides,
  });

  beforeEach(async () => {
    // Create mocks for dependencies
    mockSessionsService = {};
    mockClientManager = {};
    mockSessionManager = {
      getStreamingRuleState: jest.fn().mockReturnValue(false),
      setStreamingRuleState: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChatUpdateHandler,
        { provide: SessionsService, useValue: mockSessionsService },
        { provide: ClientManager, useValue: mockClientManager },
        { provide: SessionManager, useValue: mockSessionManager },
      ],
    }).compile();

    handler = module.get<ChatUpdateHandler>(ChatUpdateHandler);

    // Suppress console logs during tests for cleaner output
    jest.spyOn(handler['logger'], 'debug').mockImplementation(() => {});
    jest.spyOn(handler['logger'], 'log').mockImplementation(() => {});
    jest.spyOn(handler['logger'], 'warn').mockImplementation(() => {});
    jest.spyOn(handler['logger'], 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(handler).toBeDefined();
  });

  describe('canHandle', () => {
    it('should return true for BackendMessageType.ChatUpdate', () => {
      const message = { type: BackendMessageType.ChatUpdate };
      expect(handler.canHandle(message)).toBe(true);
    });

    it('should return true for messages with responses array', () => {
      const message = { responses: [] };
      expect(handler.canHandle(message)).toBe(true);
    });

    it('should return true for messages with artifacts field', () => {
      const message = { artifacts: {} };
      expect(handler.canHandle(message)).toBe(true);
    });

    it('should return true for messages with done: true', () => {
      const message = { done: true };
      expect(handler.canHandle(message)).toBe(true);
    });

    it('should return false for other message types', () => {
      const message = { type: BackendMessageType.Connection };
      expect(handler.canHandle(message)).toBe(false);
    });

    it('should return false for messages without characteristic fields', () => {
      const message = { type: 'UNKNOWN', someField: 'value' };
      expect(handler.canHandle(message)).toBe(false);
    });

    it('should return false for empty messages', () => {
      const message = {};
      expect(handler.canHandle(message)).toBe(false);
    });
  });

  describe('transform', () => {
    it('should transform a complete backend chat message correctly', async () => {
      // Arrange
      const responses = [
        createBackendChatResponse({
          id: 'response-1',
          type: 'text',
          content: 'First response',
          status: 'completed',
          done: false,
        }),
        createBackendChatResponse({
          id: 'response-2',
          type: 'status',
          content: 'Processing complete',
          status: 'completed',
          done: true,
        }),
      ];

      const detectionRule = createDetectionRule();
      const backendMessage = createBackendChatMessage({
        payload: {
          status: 'completed',
          responses: responses,
          artifacts: { detection_rule: detectionRule },
          done: true,
        },
      });

      mockSessionManager.getStreamingRuleState = jest
        .fn()
        .mockReturnValue(false);

      // Act
      const result = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          type: UiOutputMessageType.ChatUpdate,
          session_id: sessionId,
          request_id: requestId,
          timestamp: timestamp,
          responses: expect.arrayContaining([
            expect.objectContaining({
              id: 'response-1',
              type: 'text',
              content: 'First response',
              status: 'completed',
              done: false,
            }),
            expect.objectContaining({
              id: 'response-2',
              type: 'status',
              content: 'Processing complete',
              status: 'completed',
              done: true,
            }),
          ]),
          artifacts: { detection_rule: detectionRule },
          done: true,
          status: 'completed',
          error: undefined,
        }),
      );

      expect(mockSessionManager.getStreamingRuleState).toHaveBeenCalledWith(
        sessionId,
      );
      expect(mockSessionManager.setStreamingRuleState).toHaveBeenCalledWith(
        sessionId,
        false,
      );
    });

    it('should handle minimal backend message with required fields only', async () => {
      // Arrange
      const minimalMessage = {
        request_id: requestId,
        payload: {
          responses: [],
          status: 'processing',
        },
      };

      mockSessionManager.getStreamingRuleState = jest
        .fn()
        .mockReturnValue(false);

      // Act
      const result = await handler.transform(
        sessionId,
        minimalMessage as unknown as Record<string, unknown>,
      );

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          type: UiOutputMessageType.ChatUpdate,
          session_id: sessionId,
          request_id: requestId,
          timestamp: undefined,
          responses: [],
          artifacts: undefined,
          done: undefined,
          status: 'processing', // Uses the status from payload
          error: undefined,
        }),
      );
    });

    it('should default status to pending when not provided', async () => {
      // Arrange
      const message = {
        type: BackendMessageType.ChatUpdate,
        timestamp: timestamp,
        request_id: requestId,
        payload: {
          responses: [],
          done: false,
          // status not provided - should default to 'pending'
        },
      };

      mockSessionManager.getStreamingRuleState = jest
        .fn()
        .mockReturnValue(false);

      // Act
      const result = await handler.transform(
        sessionId,
        message as unknown as Record<string, unknown>,
      );

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          status: undefined,
        }),
      );
    });

    it('should handle empty responses array', async () => {
      // Arrange
      const message = createBackendChatMessage({
        payload: {
          status: 'waiting',
          responses: [],
          done: false,
        },
      });

      mockSessionManager.getStreamingRuleState = jest
        .fn()
        .mockReturnValue(false);

      // Act
      const result = await handler.transform(
        sessionId,
        message as unknown as Record<string, unknown>,
      );

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          responses: [],
          status: 'waiting',
          done: false,
        }),
      );
    });

    it('should handle missing payload gracefully', async () => {
      // Arrange
      const message = {
        type: BackendMessageType.ChatUpdate,
        request_id: requestId,
        timestamp: timestamp,
        // payload is missing
      };

      mockSessionManager.getStreamingRuleState = jest
        .fn()
        .mockReturnValue(false);

      // Act
      const result = await handler.transform(
        sessionId,
        message as unknown as Record<string, unknown>,
      );

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          type: UiOutputMessageType.ChatUpdate,
          session_id: sessionId,
          request_id: requestId,
          timestamp: timestamp,
          responses: [],
          artifacts: undefined,
          done: undefined,
          status: undefined,
          error: undefined,
        }),
      );
    });

    it('should include error field when present', async () => {
      // Arrange
      const error = { code: 500, message: 'Internal server error' };
      const message = createBackendChatMessage({
        error: error,
        payload: {
          responses: [],
          status: 'error',
        },
      });

      mockSessionManager.getStreamingRuleState = jest
        .fn()
        .mockReturnValue(false);

      // Act
      const result = await handler.transform(
        sessionId,
        message as unknown as Record<string, unknown>,
      );

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          error: error,
          status: 'error',
        }),
      );
    });

    describe('streaming rule state management', () => {
      it('should filter responses when currently streaming a rule', async () => {
        // Arrange
        const responses = [
          createBackendChatResponse({
            id: 'response-1',
            content: 'Normal response',
          }),
          createBackendChatResponse({
            id: 'response-2',
            content: 'Another response',
          }),
        ];

        const message = createBackendChatMessage({
          payload: {
            responses: responses,
            status: 'processing',
          },
        });

        mockSessionManager.getStreamingRuleState = jest
          .fn()
          .mockReturnValue(true); // Currently streaming

        // Act
        const result = await handler.transform(
          sessionId,
          message as unknown as Record<string, unknown>,
        );

        // Assert
        expect(result).toEqual(
          expect.objectContaining({
            responses: [], // All responses should be filtered out
          }),
        );
        expect(mockSessionManager.setStreamingRuleState).toHaveBeenCalledWith(
          sessionId,
          true,
        );
      });

      it('should start streaming rule when response content starts with ```', async () => {
        // Arrange
        const responses = [
          createBackendChatResponse({
            id: 'response-1',
            content: 'Normal response',
          }),
          createBackendChatResponse({
            id: 'response-2',
            content: '```sigma\ntitle: Test Rule',
          }),
        ];

        const message = createBackendChatMessage({
          payload: {
            responses: responses,
            status: 'processing',
          },
        });

        mockSessionManager.getStreamingRuleState = jest
          .fn()
          .mockReturnValue(false);

        // Act
        const result = await handler.transform(
          sessionId,
          message as unknown as Record<string, unknown>,
        );

        // Assert
        const chatResult = result as ChatUpdateDto;
        expect(chatResult).toEqual(
          expect.objectContaining({
            responses: expect.arrayContaining([
              expect.objectContaining({
                id: 'response-1',
                content: 'Normal response',
              }),
            ]),
          }),
        );
        expect(chatResult.responses).toHaveLength(1); // Second response should be filtered
        expect(mockSessionManager.setStreamingRuleState).toHaveBeenCalledWith(
          sessionId,
          true,
        );
      });

      it('should end streaming rule when response content ends with ```', async () => {
        // Arrange
        const responses = [
          createBackendChatResponse({
            id: 'response-1',
            content: 'detection:\n  condition: test\n```',
          }),
          createBackendChatResponse({
            id: 'response-2',
            content: 'Post-rule response',
          }),
        ];

        const message = createBackendChatMessage({
          payload: {
            responses: responses,
            status: 'processing',
          },
        });

        mockSessionManager.getStreamingRuleState = jest
          .fn()
          .mockReturnValue(true); // Currently streaming

        // Act
        const result = await handler.transform(
          sessionId,
          message as unknown as Record<string, unknown>,
        );

        // Assert
        const chatResult = result as ChatUpdateDto;
        expect(chatResult).toEqual(
          expect.objectContaining({
            responses: expect.arrayContaining([
              expect.objectContaining({
                id: 'response-2',
                content: 'Post-rule response',
              }),
            ]),
          }),
        );
        expect(chatResult.responses).toHaveLength(1); // First response should be filtered, second should be included
        expect(mockSessionManager.setStreamingRuleState).toHaveBeenCalledWith(
          sessionId,
          false,
        );
      });

      it('should handle mixed streaming rule scenarios correctly', async () => {
        // Arrange
        const responses = [
          createBackendChatResponse({
            id: 'response-1',
            content: 'Normal response before rule',
          }),
          createBackendChatResponse({
            id: 'response-2',
            content: '```sigma\ntitle: Test Rule',
          }),
          createBackendChatResponse({
            id: 'response-3',
            content: 'detection:\n  condition: test',
          }),
          createBackendChatResponse({
            id: 'response-4',
            content: 'more rule content\n```',
          }),
          createBackendChatResponse({
            id: 'response-5',
            content: 'Normal response after rule',
          }),
        ];

        const message = createBackendChatMessage({
          payload: {
            responses: responses,
            status: 'processing',
          },
        });

        mockSessionManager.getStreamingRuleState = jest
          .fn()
          .mockReturnValue(false);

        // Act
        const result = await handler.transform(
          sessionId,
          message as unknown as Record<string, unknown>,
        );

        // Assert
        const chatResult = result as ChatUpdateDto;
        expect(chatResult).toEqual(
          expect.objectContaining({
            responses: expect.arrayContaining([
              expect.objectContaining({
                id: 'response-1',
                content: 'Normal response before rule',
              }),
              expect.objectContaining({
                id: 'response-5',
                content: 'Normal response after rule',
              }),
            ]),
          }),
        );
        expect(chatResult.responses).toHaveLength(2); // Only non-rule responses should be included
        expect(mockSessionManager.setStreamingRuleState).toHaveBeenCalledWith(
          sessionId,
          false,
        );
      });
    });

    describe('response transformation', () => {
      it('should transform response with all fields', async () => {
        // Arrange
        const artifacts = {
          detection_cards: [{ id: 'card-1' }],
          thought: {
            id: 'thought-1',
            status: 'completed',
            content: 'Thinking...',
            elapsed_time: 1000,
            replace_content: false,
            done: true,
          },
        };

        const response = createBackendChatResponse({
          id: 'response-1',
          type: 'text',
          content: 'Test content',
          artifacts: artifacts,
          status: 'completed',
          done: true,
        });

        const message = createBackendChatMessage({
          payload: {
            responses: [response],
            status: 'completed',
          },
        });

        mockSessionManager.getStreamingRuleState = jest
          .fn()
          .mockReturnValue(false);

        // Act
        const result = await handler.transform(
          sessionId,
          message as unknown as Record<string, unknown>,
        );

        // Assert
        expect(result).toEqual(
          expect.objectContaining({
            responses: expect.arrayContaining([
              expect.objectContaining({
                id: 'response-1',
                type: 'text',
                content: 'Test content',
                artifacts: artifacts,
                status: 'completed',
                done: true,
              }),
            ]),
          }),
        );
      });

      it('should transform response with minimal fields', async () => {
        // Arrange
        const response = {
          id: 'response-1',
          // Only id is provided, other fields are undefined
        };

        const message = createBackendChatMessage({
          payload: {
            responses: [response],
            status: 'processing',
          },
        });

        mockSessionManager.getStreamingRuleState = jest
          .fn()
          .mockReturnValue(false);

        // Act
        const result = await handler.transform(
          sessionId,
          message as unknown as Record<string, unknown>,
        );

        // Assert
        expect(result).toEqual(
          expect.objectContaining({
            responses: expect.arrayContaining([
              expect.objectContaining({
                id: 'response-1',
                type: undefined,
                content: undefined,
                artifacts: undefined,
                status: undefined,
                done: undefined,
              }),
            ]),
          }),
        );
      });
    });
  });
});
