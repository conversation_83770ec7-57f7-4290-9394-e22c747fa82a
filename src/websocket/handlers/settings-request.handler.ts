import {
  Injectable,
  Logger,
  ValidationPipe,
  NotFoundException,
} from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { MessageHandler } from '../interfaces/message-handler.interface';
import { SettingsRequestDto } from '../dto/settings-request.dto';
import { ClientManager, WebSocketWithId } from '../managers/client.manager';
import { ErrorHandlerService } from '../services/error-handler.service';
import { BackendWsService } from '../services/backend-ws.service';
import { SessionsService } from '../../sessions/sessions.service';
import { StandardErrorCode } from '../dto/common.dto';
import { UiInputMessageType } from '../constants/message-types.enum';

@Injectable()
export class SettingsRequestHandler
  implements MessageHandler<SettingsRequestDto>
{
  private readonly logger = new Logger(SettingsRequestHandler.name);
  private readonly validationPipe: ValidationPipe;

  constructor(
    private readonly clientManager: ClientManager,
    private readonly errorHandlerService: ErrorHandlerService,
    private readonly backendWsService: BackendWsService,
    private readonly sessionsService: SessionsService,
  ) {
    this.validationPipe = new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    });
  }

  canHandle(message: Record<string, unknown>): boolean {
    return message.type === UiInputMessageType.SetSettings;
  }

  async validate(
    message: Record<string, unknown>,
  ): Promise<SettingsRequestDto> {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const validatedDto = await this.validationPipe.transform(message, {
      type: 'body',
      metatype: SettingsRequestDto,
    });
    return validatedDto as SettingsRequestDto;
  }

  async handle(
    client: WebSocketWithId,
    message: SettingsRequestDto,
  ): Promise<void> {
    const clientId = client.clientId;
    const clientData = this.clientManager.getClientData(clientId);

    if (
      !clientData ||
      clientData.status !== 'ready' ||
      !clientData.authenticated
    ) {
      this.logger.warn(
        `[${clientId}] SettingsRequestHandler received request from unready/unauth client.`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Client not ready or not authenticated',
        StandardErrorCode.UNAUTHORIZED,
        message.session_id,
        message.request_id,
      );
      return;
    }

    const sessionId = message.session_id;
    const requestId = message.request_id || uuidv4();
    message.request_id = requestId;

    if (sessionId !== clientData.sessionId) {
      this.logger.error(
        `[${clientId}] Session ID mismatch in SettingsRequestHandler! Client data: ${clientData.sessionId}, message: ${sessionId}`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Session ID mismatch',
        StandardErrorCode.INVALID_REQUEST,
        clientData.sessionId,
        requestId,
      );
      return;
    }

    // Verify session exists
    try {
      await this.sessionsService.getSessionById(clientData.userId, sessionId);
    } catch (error) {
      if (error instanceof NotFoundException) {
        this.logger.error(`[${clientId}] Session not found for ${sessionId}`);
        this.errorHandlerService.sendErrorToClient(
          client,
          'Session not found',
          StandardErrorCode.SESSION_NOT_FOUND,
          sessionId,
          requestId,
        );
        return;
      }
      this.logger.error(
        `[${clientId}] Error retrieving session ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Error processing session data',
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        sessionId,
        requestId,
      );
      return;
    }

    this.logger.log(
      `[${clientId}] Forwarding SET_SETTINGS (ReqID: ${requestId}) for session ${sessionId} to backend.`,
    );
    try {
      this.backendWsService.sendSettingsRequest(
        sessionId,
        clientData.userId,
        message,
      );
    } catch (error) {
      this.logger.error(
        `[${clientId}] Failed to send SET_SETTINGS to backend: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Failed to process request',
        StandardErrorCode.BACKEND_UNAVAILABLE,
        sessionId,
        requestId,
      );
    }
  }
}
