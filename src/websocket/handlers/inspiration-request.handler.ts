import { Injectable, Logger, ValidationPipe } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { MessageHandler } from '../interfaces/message-handler.interface';
import { InspirationRequestDto } from '../dto/inspiration-request.dto';
import { ClientManager, WebSocketWithId } from '../managers/client.manager';
import { SessionManager } from '../managers/session.manager';
import { ErrorHandlerService } from '../services/error-handler.service';
import { BackendWsService } from '../services/backend-ws.service';
import { StandardErrorCode } from '../dto/common.dto';
import { SessionsService } from '../../sessions/sessions.service';
import { UiInputMessageType } from '../constants/message-types.enum';

@Injectable()
export class InspirationRequestHandler
  implements MessageHandler<InspirationRequestDto>
{
  private readonly logger = new Logger(InspirationRequestHandler.name);
  private readonly validationPipe: ValidationPipe;

  constructor(
    private readonly clientManager: ClientManager,
    private readonly sessionManager: SessionManager,
    private readonly errorHandlerService: ErrorHandlerService,
    private readonly backendWsService: BackendWsService,
    private readonly sessionsService: SessionsService,
  ) {
    this.validationPipe = new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    });
  }

  canHandle(message: Record<string, unknown>): boolean {
    return message.type === UiInputMessageType.InspirationRequest;
  }

  async validate(
    message: Record<string, unknown>,
  ): Promise<InspirationRequestDto> {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const validatedDto = await this.validationPipe.transform(message, {
      type: 'body',
      metatype: InspirationRequestDto,
    });
    return validatedDto as InspirationRequestDto;
  }

  // eslint-disable-next-line @typescript-eslint/require-await
  async handle(
    client: WebSocketWithId,
    message: InspirationRequestDto,
  ): Promise<void> {
    const clientId = client.clientId;
    const clientData = this.clientManager.getClientData(clientId);

    if (
      !clientData ||
      clientData.status !== 'ready' ||
      !clientData.authenticated
    ) {
      this.logger.warn(
        `[${clientId}] InspirationRequestHandler received request from unready/unauth client.`,
      );
      const errorSessionId = clientData
        ? clientData.sessionId
        : message.session_id;
      this.errorHandlerService.sendErrorToClient(
        client,
        'Client not ready or not authenticated',
        StandardErrorCode.UNAUTHORIZED,
        errorSessionId,
        message.request_id,
      );
      return;
    }

    const sessionId = message.session_id;
    const requestId = message.request_id || uuidv4();
    message.request_id = requestId; // Ensure DTO has ID

    if (sessionId !== clientData.sessionId) {
      this.logger.error(
        `[${clientId}] Session ID mismatch! Client data: ${clientData.sessionId}, message: ${sessionId}`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Session ID mismatch',
        StandardErrorCode.INVALID_REQUEST,
        clientData.sessionId,
        requestId,
      );
      return;
    }

    this.logger.log(
      `[${clientId}] Forwarding INSPIRATION_REQUEST (ReqID: ${requestId}) for session ${sessionId} to backend.`,
    );
    try {
      // Use the specific method from BackendWsService
      this.backendWsService.sendInspirationRequest(
        sessionId,
        clientData.userId,
        message, // Pass the validated DTO
      );
    } catch (error) {
      this.logger.error(
        `[${clientId}] Failed to send INSPIRATION_REQUEST to backend: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      this.errorHandlerService.sendErrorToClient(
        client,
        'Failed to process inspiration request',
        StandardErrorCode.BACKEND_UNAVAILABLE, // Or INSPIRATION_PROCESSING_FAILED?
        sessionId,
        requestId,
      );
    }
  }
}
