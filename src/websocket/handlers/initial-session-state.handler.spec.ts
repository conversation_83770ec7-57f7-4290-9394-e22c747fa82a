import { Test, TestingModule } from '@nestjs/testing';
import { InitialSessionStateHandler } from './initial-session-state.handler';
import { SessionsService } from '../../sessions/sessions.service';
import { ClientManager } from '../managers/client.manager';
import { NotFoundException } from '@nestjs/common';
import { InitialSessionStateDto } from '../dto/session-message.dto';
import {
  UiOutputMessageType,
  BackendMessageType,
} from '../constants/message-types.enum';
import { DetectionRule, Inspiration } from '../dto/common.dto';
import { SupportedLanguage } from 'src/common/languages';
import {
  BackendConnectionMessage,
  BackendConnectionPayload,
  BackendMessage,
} from '../dto/backend-message.dto';

// Mocks
const mockSessionsService = {
  getSessionById: jest.fn(),
  updateSessionTitle: jest.fn(),
};

const mockClientManager = {
  getAllClientIds: jest.fn(),
  getClientData: jest.fn(),
  getAllClientIdsArray: jest.fn(() => ['client-1']), // Helper for iteration
};

describe('InitialSessionStateHandler', () => {
  let handler: InitialSessionStateHandler;
  let sessionsService: SessionsService;
  let clientManager: ClientManager;

  beforeEach(async () => {
    // Reset mocks before each test
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InitialSessionStateHandler,
        { provide: SessionsService, useValue: mockSessionsService },
        { provide: ClientManager, useValue: mockClientManager },
      ],
    }).compile();

    handler = module.get<InitialSessionStateHandler>(
      InitialSessionStateHandler,
    );
    sessionsService = module.get<SessionsService>(SessionsService);
    clientManager = module.get<ClientManager>(ClientManager);

    // Default mock implementations
    mockClientManager.getAllClientIds.mockReturnValue(new Set(['client-1']));
    mockClientManager.getClientData.mockReturnValue({
      userId: 'default_user',
      sessionId: 'default_session',
      client: {}, // Mock client socket if needed
    });
  });

  it('should be defined', () => {
    expect(handler).toBeDefined();
  });

  describe('canHandle', () => {
    it('should return true for messages with type Connection', () => {
      const message = { type: BackendMessageType.Connection };
      expect(handler.canHandle(message)).toBe(true);
    });

    it('should return true for messages with characteristic fields', () => {
      const message = { inspirations_token_limit: 100, language: 'kql' };
      expect(handler.canHandle(message)).toBe(true);
    });

    it('should return false for messages with wrong type', () => {
      const message = { type: BackendMessageType.ChatUpdate };
      expect(handler.canHandle(message)).toBe(false);
    });

    it('should return false for messages missing characteristic fields', () => {
      const message = { language: 'kql' };
      expect(handler.canHandle(message)).toBe(false);
    });
  });

  describe('transform', () => {
    const sessionId = 'abc123';
    const userId = 'bobby_jones';

    // Helper to create a complete BackendConnectionPayload
    const createBackendPayload = (
      overrides: Partial<BackendConnectionPayload> = {},
    ): BackendConnectionPayload => ({
      inspirations_token_limit: 800000,
      technologies: ['Windows', 'Linux'],
      security_tools: ['Splunk', 'Elastic'],
      settings: {
        detection_language: SupportedLanguage.SIGMA,
        detection_verbosity: 'medium',
      },
      inspirations: [],
      messages: [],
      artifacts: undefined,
      ...overrides,
    });

    it('should correctly transform a complex backend Connection message with artifacts and inspirations', async () => {
      // Arrange: Define Backend Input
      const detectionRule: DetectionRule = {
        id: '1',
        type: SupportedLanguage.SIGMA,
        content:
          'title: Test Rule\ndetection:\n  selection:\n    process: test\n  condition: selection',
      };

      const inspirations: Inspiration[] = [
        {
          id: '7740ff2b-1983-417c-a07a-b85ee96371a4',
          hash: 'c9baf8499dfd21bf9e74a08f4c6d10ad12cc4b54a7a33bb18f6bf071c3fa50d9',
          type: 'url',
          tokens: 6184,
          success: true,
          error: null,
        },
      ];

      const messages: BackendMessage[] = [
        {
          id: '1',
          prompt: 'Write a rule to detect email exfiltration',
          responses: [
            {
              id: '1',
              type: 'status',
              content: 'Analyzing request...',
            },
            {
              id: '2',
              type: 'text',
              content: 'Generated detection rule',
            },
          ],
          inspirations: ['7740ff2b-1983-417c-a07a-b85ee96371a4'],
        },
      ];

      const backendPayload = createBackendPayload({
        inspirations,
        messages,
        artifacts: { detection_rule: detectionRule },
      });

      const backendMessage: BackendConnectionMessage = {
        type: BackendMessageType.Connection,
        payload: backendPayload,
        timestamp: new Date().toISOString(),
      };

      // Arrange: Define Expected UI Output
      const expectedUiOutput: InitialSessionStateDto = {
        type: UiOutputMessageType.InitialSessionState,
        session_id: sessionId,
        request_id: undefined,
        session_name: null, // No existing session data
        language: SupportedLanguage.SIGMA,
        language_locked: false, // Default
        chat_history: messages,
        inspirations: inspirations,
        artifacts: { detection_rule: detectionRule },
        settings: backendPayload.settings,
        supported_technologies: backendPayload.technologies,
        supported_security_tools: backendPayload.security_tools,
        inspirations_token_limit: backendPayload.inspirations_token_limit,
      };

      // Arrange: Configure Mocks
      mockClientManager.getClientData.mockReturnValue({ userId, sessionId });
      mockSessionsService.getSessionById.mockResolvedValue(null); // No existing session

      // Act: Call the transform method
      const actualOutput = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      // Assert: Check the output matches the expected structure
      expect(actualOutput).toEqual(expectedUiOutput);
      expect(sessionsService.getSessionById).toHaveBeenCalledWith(
        userId,
        sessionId,
      );
      expect(clientManager.getClientData).toHaveBeenCalled();
    });

    it('should enrich state with data from SessionsService if found', async () => {
      // Arrange
      const backendPayload = createBackendPayload({
        settings: {
          detection_language: SupportedLanguage.KQL,
        },
      });

      const backendMessage: BackendConnectionMessage = {
        type: BackendMessageType.Connection,
        payload: backendPayload,
        timestamp: new Date().toISOString(),
      };

      const sessionDataFromDb = {
        session_id: sessionId,
        user_id: userId,
        session_title: 'My Test Session', // Title from DB
        language: SupportedLanguage.SIGMA, // Language from DB (should be overridden by backend)
        language_locked: true, // Locked status from DB
        title_generated: false,
        created_at: new Date().toISOString(),
        last_updated_at: new Date().toISOString(),
      };

      const expectedUiOutput: InitialSessionStateDto = {
        type: UiOutputMessageType.InitialSessionState,
        session_id: sessionId,
        request_id: undefined,
        session_name: sessionDataFromDb.session_title, // From DB
        language: SupportedLanguage.KQL, // From Backend (overrides DB)
        language_locked: sessionDataFromDb.language_locked, // From DB
        chat_history: [],
        inspirations: [],
        artifacts: undefined,
        settings: backendPayload.settings,
        supported_technologies: backendPayload.technologies,
        supported_security_tools: backendPayload.security_tools,
        inspirations_token_limit: backendPayload.inspirations_token_limit,
      };

      mockClientManager.getClientData.mockReturnValue({ userId, sessionId });
      mockSessionsService.getSessionById.mockResolvedValue(sessionDataFromDb);

      // Act
      const actualOutput = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      // Assert
      expect(actualOutput).toEqual(expectedUiOutput);
      expect(sessionsService.getSessionById).toHaveBeenCalledWith(
        userId,
        sessionId,
      );
    });

    it('should handle session retrieval errors gracefully', async () => {
      // Arrange
      const backendPayload = createBackendPayload({
        settings: {
          detection_language: SupportedLanguage.SPL,
        },
        artifacts: {
          detection_rule: {
            id: '1',
            type: SupportedLanguage.SPL,
            content: 'search index=main | stats count',
          },
        },
      });

      const backendMessage: BackendConnectionMessage = {
        type: BackendMessageType.Connection,
        payload: backendPayload,
        timestamp: new Date().toISOString(),
      };

      const expectedUiOutput: InitialSessionStateDto = {
        type: UiOutputMessageType.InitialSessionState,
        session_id: sessionId,
        request_id: undefined,
        session_name: null, // Error fetching session
        language: SupportedLanguage.SPL, // Use backend value
        language_locked: false, // Default as session fetch failed
        chat_history: [],
        inspirations: [],
        artifacts: backendPayload.artifacts,
        settings: backendPayload.settings,
        supported_technologies: backendPayload.technologies,
        supported_security_tools: backendPayload.security_tools,
        inspirations_token_limit: backendPayload.inspirations_token_limit,
      };

      mockClientManager.getClientData.mockReturnValue({ userId, sessionId });
      mockSessionsService.getSessionById.mockRejectedValue(
        new Error('DB connection failed'),
      ); // Simulate generic error

      // Act
      const actualOutput = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      // Assert
      expect(actualOutput).toEqual(expectedUiOutput);
      expect(sessionsService.getSessionById).toHaveBeenCalledWith(
        userId,
        sessionId,
      );
    });

    it('should handle NotFoundException from session retrieval gracefully', async () => {
      // Arrange
      const backendPayload = createBackendPayload();
      const backendMessage: BackendConnectionMessage = {
        type: BackendMessageType.Connection,
        payload: backendPayload,
        timestamp: new Date().toISOString(),
      };

      const expectedUiOutput: InitialSessionStateDto = {
        type: UiOutputMessageType.InitialSessionState,
        session_id: sessionId,
        request_id: undefined,
        session_name: null, // Session not found
        language: SupportedLanguage.SIGMA, // Use backend value from settings
        language_locked: false, // Default as session not found
        chat_history: [],
        inspirations: [],
        artifacts: undefined,
        settings: backendPayload.settings,
        supported_technologies: backendPayload.technologies,
        supported_security_tools: backendPayload.security_tools,
        inspirations_token_limit: backendPayload.inspirations_token_limit,
      };

      mockClientManager.getClientData.mockReturnValue({ userId, sessionId });
      mockSessionsService.getSessionById.mockRejectedValue(
        new NotFoundException('Session not found'),
      );

      // Act
      const actualOutput = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      // Assert
      expect(actualOutput).toEqual(expectedUiOutput);
      expect(sessionsService.getSessionById).toHaveBeenCalledWith(
        userId,
        sessionId,
      );
    });

    it('should handle case where no client is found for the session', async () => {
      // Arrange
      const backendPayload = createBackendPayload({
        settings: {
          detection_language: SupportedLanguage.KQL,
        },
      });

      const backendMessage: BackendConnectionMessage = {
        type: BackendMessageType.Connection,
        payload: backendPayload,
        timestamp: new Date().toISOString(),
      };

      const expectedUiOutput: InitialSessionStateDto = {
        type: UiOutputMessageType.InitialSessionState,
        session_id: sessionId,
        request_id: undefined,
        session_name: null, // No client -> no user -> no session lookup
        language: SupportedLanguage.KQL, // Use backend value
        language_locked: false, // Default
        chat_history: [],
        inspirations: [],
        artifacts: undefined,
        settings: backendPayload.settings,
        supported_technologies: backendPayload.technologies,
        supported_security_tools: backendPayload.security_tools,
        inspirations_token_limit: backendPayload.inspirations_token_limit,
      };

      // Simulate findUserIdForSession returning null
      mockClientManager.getAllClientIds.mockReturnValue(
        new Set(['other-client']),
      );
      mockClientManager.getClientData.mockReturnValue({
        userId: 'other_user',
        sessionId: 'other_session',
      }); // Doesn't match

      // Act
      const actualOutput = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      // Assert
      expect(actualOutput).toEqual(expectedUiOutput);
      expect(sessionsService.getSessionById).not.toHaveBeenCalled(); // Should not be called if no user found
    });

    it('should handle message with request_id', async () => {
      // Arrange
      const requestId = 'test-request-123';
      const backendPayload = createBackendPayload();
      const backendMessage: BackendConnectionMessage = {
        type: BackendMessageType.Connection,
        payload: backendPayload,
        timestamp: new Date().toISOString(),
        request_id: requestId,
      };

      mockClientManager.getClientData.mockReturnValue({ userId, sessionId });
      mockSessionsService.getSessionById.mockResolvedValue(null);

      // Act
      const actualOutput = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      // Assert
      expect(actualOutput?.request_id).toBe(requestId);
    });

    it('should handle missing payload gracefully', async () => {
      // Arrange
      const backendMessage: BackendConnectionMessage = {
        type: BackendMessageType.Connection,
        timestamp: new Date().toISOString(),
        // payload is undefined
      };

      const expectedUiOutput: InitialSessionStateDto = {
        type: UiOutputMessageType.InitialSessionState,
        session_id: sessionId,
        request_id: undefined,
        session_name: null,
        language: null, // No payload settings
        language_locked: false,
        chat_history: [],
        inspirations: [],
        artifacts: undefined,
        settings: undefined,
        supported_technologies: undefined,
        supported_security_tools: undefined,
        inspirations_token_limit: undefined,
      };

      mockClientManager.getClientData.mockReturnValue({ userId, sessionId });
      mockSessionsService.getSessionById.mockResolvedValue(null);

      // Act
      const actualOutput = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      // Assert
      expect(actualOutput).toEqual(expectedUiOutput);
    });

    it('should prioritize session language over backend settings when no backend language is provided', async () => {
      // Arrange
      const backendPayload = createBackendPayload({
        settings: {
          // No detection_language in settings
          detection_verbosity: 'high',
        },
      });

      const backendMessage: BackendConnectionMessage = {
        type: BackendMessageType.Connection,
        payload: backendPayload,
        timestamp: new Date().toISOString(),
      };

      const sessionDataFromDb = {
        session_id: sessionId,
        user_id: userId,
        session_title: 'Test Session',
        language: SupportedLanguage.KQL, // Session has KQL
        language_locked: true,
        title_generated: false,
        created_at: new Date().toISOString(),
        last_updated_at: new Date().toISOString(),
      };

      mockClientManager.getClientData.mockReturnValue({ userId, sessionId });
      mockSessionsService.getSessionById.mockResolvedValue(sessionDataFromDb);

      // Act
      const actualOutput = await handler.transform(
        sessionId,
        backendMessage as unknown as Record<string, unknown>,
      );

      // Assert
      expect((actualOutput as InitialSessionStateDto)?.language).toBe(
        SupportedLanguage.KQL,
      ); // Should use session language
      expect((actualOutput as InitialSessionStateDto)?.language_locked).toBe(
        true,
      ); // Should use session locked status
    });
  });
});
