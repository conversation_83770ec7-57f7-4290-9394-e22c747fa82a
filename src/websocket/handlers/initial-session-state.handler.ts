import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { IBackendMessageHandler } from './backend-message-handler.interface';
import { UiOutputMessage } from '../dto/ui-output-message.dto';
import { SessionsService } from '../../sessions/sessions.service';
import { ClientManager } from '../managers/client.manager';
import { BackendConnectionMessage } from '../dto/backend-message.dto';
import { InitialSessionStateDto } from '../dto/session-message.dto';
import { SessionMetadataDto } from '../../sessions/dto';
import {
  BackendMessageType,
  UiOutputMessageType,
} from '../constants/message-types.enum';

@Injectable()
export class InitialSessionStateHandler implements IBackendMessageHandler {
  private readonly logger = new Logger(InitialSessionStateHandler.name);

  constructor(
    private readonly sessionsService: SessionsService,
    private readonly clientManager: ClientManager,
  ) {}

  canHandle(message: Record<string, unknown>): boolean {
    // Check for type or specific fields indicative of S2C_CONNECTION
    return (
      message.type === BackendMessageType.Connection ||
      ('inspirations_token_limit' in message && 'language' in message)
    );
  }

  async transform(
    sessionId: string,
    message: Record<string, unknown>,
  ): Promise<UiOutputMessage | null> {
    this.logger.debug(`[${sessionId}] Handling S2C_CONNECTION`);
    const backendState = message as Partial<BackendConnectionMessage>;
    const requestId =
      typeof message.request_id === 'string' ? message.request_id : undefined;

    let sessionData: SessionMetadataDto | null = null;
    const userId = this.findUserIdForSession(sessionId);

    if (userId) {
      try {
        sessionData = await this.sessionsService.getSessionById(
          userId,
          sessionId,
        );
      } catch (error) {
        if (!(error instanceof NotFoundException)) {
          this.logger.error(
            `[${sessionId}] Error retrieving session data for user ${userId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
        }
        // Continue with partial state if session retrieval fails (but user was found)
      }
    } else {
      this.logger.warn(
        `[${sessionId}] No active clients found for session during enrichment.`,
      );
    }

    if (!sessionData && userId) {
      // Log if user was found but session data wasn't (e.g., NotFoundException was caught)
      this.logger.warn(
        `[${sessionId}] Session data not found for user ${userId} during enrichment.`,
      );
    }

    const uiState: InitialSessionStateDto = {
      type: UiOutputMessageType.InitialSessionState,
      session_id: sessionId,
      request_id: requestId,
      session_name: sessionData?.session_title ?? null,
      language:
        backendState.payload?.settings?.detection_language ??
        sessionData?.language ??
        null,
      language_locked: sessionData?.language_locked ?? false,
      chat_history: backendState.payload?.messages ?? [],
      inspirations: backendState.payload?.inspirations ?? [],
      artifacts: backendState.payload?.artifacts,
      settings: backendState.payload?.settings,
      supported_technologies: backendState.payload?.technologies,
      supported_security_tools: backendState.payload?.security_tools,
      inspirations_token_limit: backendState.payload?.inspirations_token_limit,
    };

    return uiState;
  }

  /**
   * Helper to find the userId associated with the first client found for a given session.
   * @param sessionId The session ID to search for.
   * @returns The userId or null if no client is found for the session.
   */
  private findUserIdForSession(sessionId: string): string | null {
    const clientsInSession = Array.from(this.clientManager.getAllClientIds())
      .map((clientId) => this.clientManager.getClientData(clientId))
      .find((clientData) => clientData && clientData.sessionId === sessionId);

    return clientsInSession?.userId ?? null;
  }
}
