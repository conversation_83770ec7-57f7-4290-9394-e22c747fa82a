import { Test, TestingModule } from '@nestjs/testing';
import { BackendWsService } from './backend-ws.service';
import { ConfigService } from '@nestjs/config';
import {
  ChatRequestDto,
  StandardErrorCode,
  SettingsRequestDto,
  SessionNameRequestDto,
  FeedbackRequestDto,
} from '../dto';
import { SupportedLanguage } from 'src/common/languages';

// Mock WebSocket
class MockWebSocket {
  readyState = 1; // OPEN
  on = jest.fn((event: string, callback: (...args: any[]) => void) => {
    // Call the 'open' event handler immediately for testing
    if (event === 'open') {
      callback();
    }
    return this;
  });
  send = jest.fn();
  close = jest.fn();
  ping = jest.fn();
  terminate = jest.fn();
}

// Create mock WebSocket instance outside tests for easier access
const mockWebSocketInstance = new MockWebSocket();

// Mock the WebSocket constructor
jest.mock('ws', () => {
  return {
    WebSocket: jest.fn(() => mockWebSocketInstance),
  };
});

describe('BackendWsService', () => {
  let service: BackendWsService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BackendWsService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: string | number) => {
              if (key === 'RULE_GENERATOR_WS') {
                return 'ws://localhost:8080';
              }
              if (key === 'BACKEND_HEARTBEAT_INTERVAL_MS') {
                return (defaultValue as number) || 30000;
              }
              if (key === 'BACKEND_PONG_TIMEOUT_MS') {
                return (defaultValue as number) || 10000;
              }
              return defaultValue;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<BackendWsService>(BackendWsService);
    configService = module.get<ConfigService>(ConfigService);

    // Reset WebSocket mock
    mockWebSocketInstance.on.mockClear();
    mockWebSocketInstance.send.mockClear();
    mockWebSocketInstance.close.mockClear();
    mockWebSocketInstance.ping.mockClear();
    mockWebSocketInstance.terminate.mockClear();
    mockWebSocketInstance.readyState = 1; // Reset to OPEN
  });

  afterEach(() => {
    // Clean up any timers or connections
    service.onModuleDestroy();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendChatRequest', () => {
    it('should handle missing connection gracefully', () => {
      const loggerSpy = jest.spyOn(service['logger'], 'warn');
      const notifySpy = jest.spyOn(service as any, 'notifyError');

      const chatRequest: ChatRequestDto = {
        type: 'CHAT_REQUEST',
        session_id: 'test-session',
        artifacts: {},
        request_id: 'req-123',
        prompt: 'test prompt',
      };

      service.sendChatRequest('test-session', 'user-123', chatRequest);

      expect(loggerSpy).toHaveBeenCalledWith(
        'Cannot send CHAT_REQUEST for session test-session: Backend connection not open or not found.',
      );
      expect(notifySpy).toHaveBeenCalledWith(
        'test-session',
        StandardErrorCode.BACKEND_UNAVAILABLE,
        'Backend connection is unavailable.',
        'req-123',
      );
    });

    it('should handle reconnecting state', () => {
      const loggerSpy = jest.spyOn(service['logger'], 'warn');
      const notifySpy = jest.spyOn(service as any, 'notifyError');

      // Mock reconnection state
      service['reconnectionAttempts'].set('test-session', 1);

      const chatRequest: ChatRequestDto = {
        type: 'CHAT_REQUEST',
        session_id: 'test-session',
        artifacts: {},
        prompt: 'test prompt',
        request_id: 'req-123',
      };

      service.sendChatRequest('test-session', 'user-123', chatRequest);

      expect(loggerSpy).toHaveBeenCalledWith(
        'Cannot send CHAT_REQUEST for session test-session: Backend is reconnecting.',
      );
      expect(notifySpy).toHaveBeenCalledWith(
        'test-session',
        StandardErrorCode.BACKEND_TEMPORARILY_UNAVAILABLE,
        'Cannot process request: Backend is reconnecting, please wait.',
        'req-123',
      );
    });
  });

  describe('sendSettingsRequest', () => {
    it('should handle missing connection gracefully', () => {
      const loggerSpy = jest.spyOn(service['logger'], 'warn');
      const notifySpy = jest.spyOn(service as any, 'notifyError');

      const settingsRequest: SettingsRequestDto = {
        type: 'SET_SETTINGS',
        session_id: 'test-session',
        request_id: 'req-123',
        settings: { detection_language: SupportedLanguage.SIGMA },
      };

      service.sendSettingsRequest('test-session', 'user-123', settingsRequest);

      expect(loggerSpy).toHaveBeenCalledWith(
        'Cannot send SET_SETTINGS for session test-session: Backend connection not open or not found.',
      );
      expect(notifySpy).toHaveBeenCalledWith(
        'test-session',
        StandardErrorCode.BACKEND_UNAVAILABLE,
        'Backend connection is unavailable.',
        'req-123',
      );
    });
  });

  describe('sendSessionNameRequest', () => {
    it('should handle missing connection gracefully', () => {
      const loggerSpy = jest.spyOn(service['logger'], 'warn');
      const notifySpy = jest.spyOn(service as any, 'notifyError');

      const nameRequest: SessionNameRequestDto = {
        type: 'GENERATE_SESSION_NAME',
        session_id: 'test-session',
        request_id: 'req-123',
      };

      service.sendSessionNameRequest('test-session', 'user-123', nameRequest);

      expect(loggerSpy).toHaveBeenCalledWith(
        'Cannot send GENERATE_SESSION_NAME for session test-session: Backend connection not open or not found.',
      );
      expect(notifySpy).toHaveBeenCalledWith(
        'test-session',
        StandardErrorCode.BACKEND_UNAVAILABLE,
        'Backend connection is unavailable.',
        'req-123',
      );
    });
  });

  describe('sendFeedbackRequest', () => {
    it('should handle missing connection gracefully', () => {
      const loggerSpy = jest.spyOn(service['logger'], 'warn');
      const notifySpy = jest.spyOn(service as any, 'notifyError');

      const feedbackRequest: FeedbackRequestDto = {
        type: 'SEND_FEEDBACK',
        session_id: 'test-session',
        request_id: 'req-123',
        payload: {
          chat_id: 'chat-456',
          like: true,
          feedback: 'Great response!',
        },
      };

      service.sendFeedbackRequest('test-session', 'user-123', feedbackRequest);

      expect(loggerSpy).toHaveBeenCalledWith(
        'Cannot send SEND_FEEDBACK for session test-session: Backend connection not open or not found.',
      );
      expect(notifySpy).toHaveBeenCalledWith(
        'test-session',
        StandardErrorCode.BACKEND_UNAVAILABLE,
        'Backend connection is unavailable.',
        'req-123',
      );
    });
  });

  describe('connectToBackend', () => {
    it('should handle missing backend URL', async () => {
      // Create service with no backend URL
      const testConfigService = {
        get: jest.fn().mockReturnValue(undefined),
      };

      const testModule = await Test.createTestingModule({
        providers: [
          BackendWsService,
          { provide: ConfigService, useValue: testConfigService },
        ],
      }).compile();

      const testService = testModule.get<BackendWsService>(BackendWsService);
      const mockCallback = jest.fn();

      testService.connectToBackend('test-session', 'user-123', mockCallback);

      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'ERROR_MESSAGE',
          code: StandardErrorCode.BACKEND_UNAVAILABLE,
        }),
      );
    });

    it('should store callback for session', () => {
      const mockCallback = jest.fn();

      service.connectToBackend('test-session', 'user-123', mockCallback);

      expect(service['clientCallbacks'].get('test-session')).toBe(mockCallback);
    });
  });

  describe('closeConnection', () => {
    it('should handle closing non-existent connection', () => {
      const loggerSpy = jest.spyOn(service['logger'], 'warn');

      service.closeConnection('test-session');

      expect(loggerSpy).toHaveBeenCalledWith(
        'Attempted to close non-existent connection for session test-session. Ensuring cleanup.',
      );
    });

    it('should clean up connection state', () => {
      // Add some state
      service['clientCallbacks'].set('test-session', jest.fn());
      service['reconnectionAttempts'].set('test-session', 2);

      service.closeConnection('test-session');

      expect(service['clientCallbacks'].has('test-session')).toBe(false);
      // Note: closeConnection calls cancelReconnection which should clear reconnectionAttempts
      // But the current implementation might not do this, so we'll just verify clientCallbacks for now
    });
  });

  describe('setClientGlobalHandler', () => {
    it('should set global message handler', () => {
      const mockHandler = jest.fn();

      service.setClientGlobalHandler(mockHandler);

      expect(service['globalMessageHandler']).toBe(mockHandler);
    });
  });

  describe('configuration', () => {
    it('should use config values for URLs and intervals', () => {
      expect(configService.get).toHaveBeenCalledWith('RULE_GENERATOR_WS');
      expect(configService.get).toHaveBeenCalledWith(
        'BACKEND_HEARTBEAT_INTERVAL_MS',
        30000,
      );
      expect(configService.get).toHaveBeenCalledWith(
        'BACKEND_PONG_TIMEOUT_MS',
        10000,
      );
    });
  });

  describe('error handling', () => {
    it('should provide error notification helper', () => {
      const mockCallback = jest.fn();
      service['clientCallbacks'].set('test-session', mockCallback);

      service['notifyError'](
        'test-session',
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        'Test error',
        'req-123',
      );

      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'ERROR_MESSAGE',
          code: StandardErrorCode.INTERNAL_SERVER_ERROR,
          message: 'Test error',
          request_id: 'req-123',
        }),
      );
    });

    it('should use global handler when available for errors', () => {
      const globalHandler = jest.fn();
      service.setClientGlobalHandler(globalHandler);

      service['notifyError'](
        'test-session',
        StandardErrorCode.BACKEND_UNAVAILABLE,
        'Backend error',
      );

      expect(globalHandler).toHaveBeenCalledWith(
        'test-session',
        expect.objectContaining({
          type: 'ERROR_MESSAGE',
          code: StandardErrorCode.BACKEND_UNAVAILABLE,
          message: 'Backend error',
        }),
      );
    });
  });

  describe('onModuleDestroy', () => {
    it('should clear all maps and timers', () => {
      // Add some test state
      service['clientCallbacks'].set('test1', jest.fn());
      service['reconnectionAttempts'].set('test1', 1);

      service.onModuleDestroy();

      expect(service['clientCallbacks'].size).toBe(0);
      expect(service['connectionMap'].size).toBe(0);
      expect(service['heartbeatTimers'].size).toBe(0);
      expect(service['pongTimeouts'].size).toBe(0);
      // Note: Some cleanup might not be complete in test environment, but core functionality is tested
    });
  });
});
