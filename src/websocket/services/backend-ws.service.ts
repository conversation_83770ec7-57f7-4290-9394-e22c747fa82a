import { Injectable, Logger, OnModule<PERSON><PERSON>roy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { WebSocket } from 'ws';
import {
  ChatRequestDto,
  InspirationRequestDto,
  CancelChatRequestDto,
  StandardErrorCode,
  ErrorMessageDto,
  BackendConnectionStatus,
  BackendChatMessage,
  BackendStatusMessageDto,
  BackendInspirationRequestDto,
  BackendChatRequestDto,
} from '../dto';
import {
  BackendSettingsRequestDto,
  SettingsRequestDto,
} from '../dto/settings-request.dto';
import {
  BackendSessionNameRequestDto,
  SessionNameRequestDto,
} from '../dto/session-name-request.dto';
import {
  BackendFeedbackRequestDto,
  FeedbackRequestDto,
} from '../dto/feedback-request.dto';
import { translateInspirationToBackendFormat } from '../utils';

// Define constants for reconnection logic
const MAX_RECONNECT_ATTEMPTS = 5;
const INITIAL_BACKOFF_MS = 1000; // 1 second
const MAX_BACKOFF_MS = 30000; // 30 seconds

export enum BackendMessageType {
  ChatRequest = 'C2S_CHAT',
  InspirationRequest = 'C2S_INSPIRATIONS',
  SettingsRequest = 'C2S_SETTINGS',
  SessionNameRequest = 'C2S_SESSION_NAME',
  FeedbackRequest = 'C2S_FEEDBACK',
}

/**
 * Service responsible for managing WebSocket connections to the backend service
 */
@Injectable()
export class BackendWsService implements OnModuleDestroy {
  private readonly logger = new Logger(BackendWsService.name);
  private readonly backendWsUrl: string;
  private readonly connectionMap = new Map<string, WebSocket>();
  private readonly clientCallbacks = new Map<string, (data: any) => void>();
  private readonly heartbeatTimers = new Map<string, NodeJS.Timeout>();
  private readonly heartbeatIntervalMs: number;
  private readonly pongTimeoutMs: number;
  private readonly pongTimeouts = new Map<string, NodeJS.Timeout>();
  private globalMessageHandler:
    | ((sessionId: string, message: any) => void)
    | null = null;

  // Reconnection state tracking
  private readonly reconnectionAttempts = new Map<string, number>();
  private readonly reconnectionTimers = new Map<string, NodeJS.Timeout>();

  constructor(private readonly configService: ConfigService) {
    const rawBackendUrl = this.configService.get<string>('RULE_GENERATOR_WS'); // Get raw value
    this.backendWsUrl = rawBackendUrl || '';
    this.logger.log(`Raw RULE_GENERATOR_WS from config: ${rawBackendUrl}`); // Log raw value
    this.logger.log(`Backend WS URL configured as: ${this.backendWsUrl}`);

    // Get heartbeat configuration
    this.heartbeatIntervalMs = this.configService.get<number>(
      'BACKEND_HEARTBEAT_INTERVAL_MS',
      30000, // Default to 30 seconds for backend connections
    );
    this.pongTimeoutMs = this.configService.get<number>(
      'BACKEND_PONG_TIMEOUT_MS',
      10000, // Default to 10 seconds timeout for pong response
    );
    this.logger.log(
      `Backend WebSocket heartbeat interval: ${this.heartbeatIntervalMs}ms`,
    );
    this.logger.log(`Backend WebSocket pong timeout: ${this.pongTimeoutMs}ms`);
  }

  /**
   * Cleanup resources when module is destroyed
   */
  onModuleDestroy(): void {
    this.logger.log('Backend WebSocket service destroying...');
    // Clear all reconnection timers first
    this.reconnectionTimers.forEach((timer) => {
      clearTimeout(timer);
    });
    this.reconnectionTimers.clear();

    // Stop heartbeats and clear related timeouts
    this.stopAllHeartbeats(); // Already clears heartbeatTimers and pongTimeouts

    // Close all active connections cleanly
    this.connectionMap.forEach((socket, sessionId) => {
      this.logger.log(`Closing backend connection for session ${sessionId}...`);
      // Prevent reconnection attempts during shutdown
      this.reconnectionAttempts.delete(sessionId);
      if (
        socket.readyState === WebSocket.OPEN ||
        socket.readyState === WebSocket.CONNECTING
      ) {
        socket.close(1000, 'Service shutting down'); // 1000 = Normal Closure
      }
    });
    this.connectionMap.clear();
    this.clientCallbacks.clear();
    this.logger.log(
      'Backend WebSocket service destroyed, all connections closed and resources cleared.',
    );
  }

  /**
   * Stop all heartbeat timers
   */
  private stopAllHeartbeats(): void {
    // Clear all heartbeat timers
    this.heartbeatTimers.forEach((timer) => {
      clearInterval(timer);
    });
    this.heartbeatTimers.clear();

    // Clear all pong timeouts
    this.pongTimeouts.forEach((timeout) => {
      clearTimeout(timeout);
    });
    this.pongTimeouts.clear();
  }

  /**
   * Set a global handler for all backend messages
   * This will be called for all sessions instead of individual callbacks
   */
  setClientGlobalHandler(
    handler: (sessionId: string, message: any) => void,
  ): void {
    this.globalMessageHandler = handler;
    this.logger.log('Global message handler registered');
  }

  /**
   * Connects to the backend WebSocket for a specific session or initiates reconnection.
   * @param sessionId The session ID
   * @param userId The user ID
   * @param language The selected language
   * @param messageCallback Callback function for messages received from backend
   * @param isReconnectionAttempt Flag indicating if this is part of a reconnection sequence
   */
  connectToBackend(
    sessionId: string,
    userId: string,
    messageCallback: (data: any) => void,
    isReconnectionAttempt = false,
  ): void {
    if (!this.backendWsUrl) {
      this.logger.error('Backend WebSocket URL not configured');
      const error: ErrorMessageDto = {
        type: 'ERROR_MESSAGE',
        session_id: sessionId,
        code: StandardErrorCode.BACKEND_UNAVAILABLE,
        message: 'Backend WebSocket service not configured',
      };
      messageCallback(error);
      return;
    }

    // Store or update connection arguments for potential reconnection
    this.clientCallbacks.set(sessionId, messageCallback);

    // If connection exists and is open/connecting, do nothing unless it's a forced reconnect call (which we don't have yet)
    const existingSocket = this.connectionMap.get(sessionId);
    if (
      existingSocket &&
      (existingSocket.readyState === WebSocket.OPEN ||
        existingSocket.readyState === WebSocket.CONNECTING) &&
      !isReconnectionAttempt
    ) {
      this.logger.log(
        `Backend connection already exists or is connecting for session ${sessionId}`,
      );
      return;
    }

    // --- Connection Logic ---
    try {
      // Construct the URL with only path parameters
      const url = `${this.backendWsUrl}/${userId}/${sessionId}`;
      this.logger.log(
        `Attempting to connect to backend WS at ${url} for session ${sessionId}${isReconnectionAttempt ? ' (Reconnection Attempt)' : ''}...`,
      );
      const backendSocket = new WebSocket(url);

      // Store the socket *immediately* to prevent race conditions
      this.connectionMap.set(sessionId, backendSocket);

      // Set up event handlers
      backendSocket.on('open', () => {
        this.handleBackendOpen(sessionId, backendSocket, isReconnectionAttempt);
      });

      backendSocket.on('pong', () => {
        this.handleBackendPong(sessionId);
      });

      backendSocket.on('message', (data) => {
        this.handleBackendMessage(sessionId, data);
      });

      backendSocket.on('error', (error: Error) => {
        this.handleBackendError(
          sessionId,
          userId,
          error,
          isReconnectionAttempt,
        );
        // Error often precedes close, but we'll handle potential reconnection in 'close'
      });

      backendSocket.on('close', (code: number, reason: Buffer) => {
        this.handleBackendClose(sessionId, userId, code, reason);
      });
    } catch (error) {
      // This catch handles synchronous errors during WebSocket object creation (e.g., invalid URL format)
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to initiate backend connection for session ${sessionId}: ${errorMessage}`,
      );
      this.notifyBackendStatus(
        sessionId,
        BackendConnectionStatus.FAILED,
        'Failed to initiate connection',
      );

      // Cleanup if socket was partially created/stored
      this.cleanupConnectionState(sessionId);

      // Schedule a reconnection attempt if this wasn't already one that failed immediately
      if (!isReconnectionAttempt) {
        this.scheduleReconnection(sessionId, userId);
      } else {
        // If the reconnection attempt itself failed immediately, schedule the *next* one
        this.scheduleReconnection(sessionId, userId);
      }
    }
  }

  // --- New Handler Methods ---

  private handleBackendOpen(
    sessionId: string,
    socket: WebSocket,
    wasReconnection: boolean,
  ): void {
    this.logger.log(
      `Backend connection established for session ${sessionId}${wasReconnection ? ' (Reconnected)' : ''}`,
    );

    // If this was a successful reconnection, reset attempts and clear timer
    if (wasReconnection) {
      this.reconnectionAttempts.delete(sessionId);
      const timer = this.reconnectionTimers.get(sessionId);
      if (timer) {
        clearTimeout(timer);
        this.reconnectionTimers.delete(sessionId);
      }
      this.notifyBackendStatus(sessionId, BackendConnectionStatus.RECONNECTED);
    } else {
      this.notifyBackendStatus(sessionId, BackendConnectionStatus.CONNECTED);
    }

    // Start heartbeat
    this.startHeartbeat(sessionId, socket);
  }

  private handleBackendPong(sessionId: string): void {
    this.logger.debug(`Received pong from backend for session ${sessionId}`);
    // Clear any pending pong timeout
    const timeout = this.pongTimeouts.get(sessionId);
    if (timeout) {
      clearTimeout(timeout);
      this.pongTimeouts.delete(sessionId);
    }
  }

  private handleBackendMessage(
    sessionId: string,
    data: Buffer | string | ArrayBuffer | Buffer[],
  ): void {
    // Log raw data using the parsing logic for safe string conversion
    try {
      const messageStringForLog = this.parseMessageData(data);
      this.logger.debug(
        `Received raw message from backend for session ${sessionId}: ${messageStringForLog}`,
      );
    } catch (logError) {
      this.logger.error(
        `Failed to stringify received backend message for logging: ${logError}`,
      );
    }

    try {
      const messageContent = this.parseMessageData(data);
      const parsedMessage = JSON.parse(messageContent) as BackendChatMessage;

      if (this.globalMessageHandler) {
        this.globalMessageHandler(sessionId, parsedMessage);
      } else {
        const callback = this.clientCallbacks.get(sessionId);
        if (callback) {
          callback(parsedMessage);
        } else {
          this.logger.warn(
            `No message callback found for session ${sessionId} to handle message.`,
          );
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Error processing backend message for session ${sessionId}: ${errorMessage}`,
      );
      this.notifyError(
        sessionId,
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        'Failed to process backend message',
      );
    }
  }

  private handleBackendError(
    sessionId: string,
    userId: string,
    error: Error,
    wasReconnectionAttempt: boolean,
  ): void {
    // Log the error regardless of whether it leads to a close event
    this.logger.error(
      `Backend WebSocket error for session ${sessionId}: ${error.message}`,
      error.stack,
    );

    // We don't schedule reconnection here because 'close' event almost always follows 'error'.
    // Reconnection logic is primarily handled in 'handleBackendClose'.
    // However, we should notify the client immediately about the error.
    // Note: This might be redundant if 'close' follows immediately with its own notification.
    // Consider if you want error-specific notifications vs. just connection status.
    // this.notifyError(sessionId, StandardErrorCode.BACKEND_UNAVAILABLE, `Backend connection error: ${error.message}`);

    // If the error occurred *during* the initial connection attempt of a reconnection cycle,
    // it might not trigger a 'close' event immediately or reliably. In this specific case,
    // ensure the reconnection cycle continues.
    if (
      wasReconnectionAttempt &&
      !this.connectionMap.get(sessionId)?.readyState
    ) {
      this.logger.warn(
        `Error during reconnection attempt for ${sessionId}, ensuring next attempt is scheduled.`,
      );
      // Clean up the failed socket attempt before scheduling next
      this.cleanupConnectionState(sessionId);
      this.scheduleReconnection(sessionId, userId);
    }
  }

  private handleBackendClose(
    sessionId: string,
    userId: string,
    code: number,
    reason: Buffer,
  ): void {
    const reasonStr = reason.toString();
    this.logger.log(
      `Backend connection closed for session ${sessionId}. Code: ${code}, Reason: ${reasonStr}`,
    );

    // Stop heartbeat and clear pong timeout for this specific session
    this.stopHeartbeat(sessionId);

    // Clean up socket reference
    this.connectionMap.delete(sessionId);

    // --- Reconnection Logic ---
    // Check if the closure was abnormal (code 1006 is common for unexpected drops)
    // or potentially recoverable server-side issues (like 1011 Internal Server Error, 1012 Service Restart)
    const isAbnormalClosure = code !== 1000 && code !== 1001 && code !== 1005; // 1000=Normal, 1001=Going Away, 1005=No Status Received

    if (isAbnormalClosure && this.clientCallbacks.has(sessionId)) {
      this.logger.warn(
        `Abnormal closure detected for session ${sessionId} (Code: ${code}). Initiating reconnection process.`,
      );
      this.notifyBackendStatus(
        sessionId,
        BackendConnectionStatus.RECONNECTING,
        `Connection lost (Code: ${code}). Attempting to reconnect...`,
      );
      this.scheduleReconnection(sessionId, userId);
    } else {
      // Normal closure or session no longer needed (args removed)
      this.logger.log(
        `Performing final cleanup for session ${sessionId} after closure (Code: ${code}).`,
      );
      // Notify clients of permanent disconnection only if it wasn't a normal close initiated by us
      if (code !== 1000) {
        this.notifyBackendStatus(
          sessionId,
          BackendConnectionStatus.DISCONNECTED,
          `Connection closed (Code: ${code})`,
        );
      }
      this.cleanupSessionCompletely(sessionId);
    }
  }

  // --- Reconnection Methods ---

  private scheduleReconnection(sessionId: string, userId: string): void {
    const currentAttempt = this.reconnectionAttempts.get(sessionId) || 0;

    if (currentAttempt >= MAX_RECONNECT_ATTEMPTS) {
      this.logger.error(
        `Max reconnection attempts reached for session ${sessionId}. Giving up.`,
      );
      this.notifyBackendStatus(
        sessionId,
        BackendConnectionStatus.FAILED,
        'Max reconnection attempts reached.',
      );
      this.cleanupSessionCompletely(sessionId); // Perform final cleanup
      return;
    }

    // Calculate backoff time
    const backoff = Math.min(
      INITIAL_BACKOFF_MS * Math.pow(2, currentAttempt),
      MAX_BACKOFF_MS,
    );
    const nextAttemptDelay =
      backoff + Math.random() * (INITIAL_BACKOFF_MS * 0.5); // Add jitter

    this.logger.log(
      `Scheduling reconnection attempt ${currentAttempt + 1} for session ${sessionId} in ${nextAttemptDelay.toFixed(0)}ms`,
    );

    // Clear existing timer just in case
    const existingTimer = this.reconnectionTimers.get(sessionId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    const timer = setTimeout(() => {
      this.attemptReconnection(sessionId, userId);
    }, nextAttemptDelay);

    this.reconnectionTimers.set(sessionId, timer);
    this.reconnectionAttempts.set(sessionId, currentAttempt + 1);
  }

  private attemptReconnection(sessionId: string, userId: string): void {
    this.logger.log(
      `Attempting reconnection for session ${sessionId} (Attempt ${this.reconnectionAttempts.get(sessionId)})...`,
    );

    const callback = this.clientCallbacks.get(sessionId);
    if (!callback) {
      this.logger.warn(
        `Cannot attempt reconnection for session ${sessionId}: message callback not found. Cleaning up.`,
      );
      this.cleanupSessionCompletely(sessionId);
      return;
    }

    // Call connectToBackend with the reconnection flag
    this.connectToBackend(
      sessionId,
      userId,
      callback,
      true, // Indicate this is a reconnection attempt
    );
  }

  /** Cancels any ongoing reconnection attempt for a session */
  private cancelReconnection(sessionId: string): void {
    const timer = this.reconnectionTimers.get(sessionId);
    if (timer) {
      clearTimeout(timer);
      this.reconnectionTimers.delete(sessionId);
      this.reconnectionAttempts.delete(sessionId); // Reset attempts
      this.logger.log(
        `Cancelled reconnection attempts for session ${sessionId}`,
      );
    }
  }

  // --- Heartbeat Methods ---

  private startHeartbeat(sessionId: string, socket: WebSocket): void {
    this.logger.debug(`Starting heartbeat for backend session ${sessionId}`);

    // Clear any existing heartbeat for this session
    this.stopHeartbeat(sessionId);

    // Set up new heartbeat timer
    const heartbeatTimer = setInterval(() => {
      this.sendPing(sessionId, socket);
    }, this.heartbeatIntervalMs);

    this.heartbeatTimers.set(sessionId, heartbeatTimer);
  }

  private stopHeartbeat(sessionId: string): void {
    // Clear heartbeat timer
    const timer = this.heartbeatTimers.get(sessionId);
    if (timer) {
      clearInterval(timer);
      this.heartbeatTimers.delete(sessionId);
      this.logger.debug(
        `Stopped heartbeat timer for backend session ${sessionId}`,
      );
    }
    // Clear any pending pong timeout
    const pongTimeout = this.pongTimeouts.get(sessionId);
    if (pongTimeout) {
      clearTimeout(pongTimeout);
      this.pongTimeouts.delete(sessionId);
      this.logger.debug(
        `Cleared pending pong timeout for backend session ${sessionId}`,
      );
    }
  }

  private sendPing(sessionId: string, socket: WebSocket): void {
    if (socket.readyState === WebSocket.OPEN) {
      try {
        this.logger.debug(`Sending ping to backend for session ${sessionId}`);

        // Send the ping
        socket.ping();

        // Set up timeout for pong response
        const timeout = setTimeout(() => {
          this.logger.warn(
            `Pong timeout for backend session ${sessionId}. Closing connection.`,
          );
          // Don't call cleanup directly, terminate will trigger the 'close' event which handles cleanup/reconnect
          socket.terminate();
        }, this.pongTimeoutMs);
        this.pongTimeouts.set(sessionId, timeout);
      } catch (error) {
        this.logger.error(
          `Failed to send ping for session ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        // If ping fails, the connection might be broken, terminate to trigger close/reconnect
        socket.terminate();
      }
    } else {
      this.logger.warn(
        `Attempted to send ping, but socket for session ${sessionId} is not open. State: ${socket?.readyState}. Stopping heartbeat.`,
      );
      this.stopHeartbeat(sessionId);
      // If socket exists but isn't open, ensure cleanup/reconnection is handled if necessary
      if (
        socket &&
        socket.readyState !== WebSocket.CLOSED &&
        socket.readyState !== WebSocket.CLOSING
      ) {
        socket.terminate(); // Force close event
      } else if (!socket) {
        this.cleanupConnectionState(sessionId); // Ensure maps are cleared if socket is missing
      }
    }
  }

  // --- Public Methods for Sending Data ---

  public sendChatRequest(
    sessionId: string,
    userId: string,
    request: ChatRequestDto,
  ): void {
    const backendConnection = this.connectionMap.get(sessionId);

    // Check connection status before sending
    if (!backendConnection || backendConnection.readyState !== WebSocket.OPEN) {
      // If reconnecting, notify UI with specific error
      if (this.reconnectionAttempts.has(sessionId)) {
        this.logger.warn(
          `Cannot send CHAT_REQUEST for session ${sessionId}: Backend is reconnecting.`,
        );
        this.notifyError(
          sessionId,
          StandardErrorCode.BACKEND_TEMPORARILY_UNAVAILABLE,
          'Cannot process request: Backend is reconnecting, please wait.',
          request.request_id,
        );
      } else {
        // If not reconnecting, it's a general unavailability
        this.logger.warn(
          `Cannot send CHAT_REQUEST for session ${sessionId}: Backend connection not open or not found.`,
        );
        this.notifyError(
          sessionId,
          StandardErrorCode.BACKEND_UNAVAILABLE,
          'Backend connection is unavailable.',
          request.request_id,
        );
      }
      return; // Stop processing the request
    }

    // Construct the backend request format
    const backendRequest: BackendChatRequestDto = {
      type: BackendMessageType.ChatRequest,
      user_id: userId,
      session_id: request.session_id,
      request_id: request.request_id ?? '', // Request ID should've been set by the service already
      timestamp: new Date().toISOString(),
      payload: {
        prompt: request.prompt,
        artifacts: request.artifacts,
      },
    };

    try {
      backendConnection.send(JSON.stringify(backendRequest));
      this.logger.debug(
        `Chat request sent to backend for session: ${sessionId}`,
        'BackendWsService',
      );
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : '';
      this.logger.error(
        `Error sending chat request to backend: ${errorMessage}`,
        errorStack,
        'BackendWsService',
      );
      // Notify about send error
      this.notifyError(
        sessionId,
        StandardErrorCode.INTERNAL_SERVER_ERROR, // Or a more specific code if possible
        `Failed to send chat request: ${errorMessage}`,
        request.request_id,
      );
    }
  }

  sendInspirationRequest(
    sessionId: string,
    userId: string,
    inspirationRequest: InspirationRequestDto,
  ): void {
    const backendSocket = this.connectionMap.get(sessionId);

    // Check connection status before sending
    if (!backendSocket || backendSocket.readyState !== WebSocket.OPEN) {
      if (this.reconnectionAttempts.has(sessionId)) {
        this.logger.warn(
          `Cannot send INSPIRATION_REQUEST for session ${sessionId}: Backend is reconnecting.`,
        );
        this.notifyError(
          sessionId,
          StandardErrorCode.BACKEND_TEMPORARILY_UNAVAILABLE,
          'Cannot process request: Backend is reconnecting, please wait.',
          inspirationRequest.request_id,
        );
      } else {
        this.logger.warn(
          `Cannot send INSPIRATION_REQUEST for session ${sessionId}: Backend connection not open or not found.`,
        );
        this.notifyError(
          sessionId,
          StandardErrorCode.BACKEND_UNAVAILABLE,
          'Backend connection is unavailable.',
          inspirationRequest.request_id,
        );
      }
      return;
    }

    // Translate UI format to backend format using utility function
    const backendRequest: BackendInspirationRequestDto =
      translateInspirationToBackendFormat(
        sessionId,
        userId,
        inspirationRequest,
      );

    this.logger.debug(
      `Sending INSPIRATION_REQUEST to backend for session ${sessionId}: ${JSON.stringify(backendRequest)}`,
    );
    try {
      backendSocket.send(JSON.stringify(backendRequest));
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Error sending inspiration request to backend: ${errorMessage}`,
        error instanceof Error ? error.stack : '',
        'BackendWsService',
      );
      this.notifyError(
        sessionId,
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        `Failed to send inspiration request: ${errorMessage}`,
        inspirationRequest.request_id,
      );
    }
  }

  sendCancelRequest(
    sessionId: string,
    userId: string,
    cancelRequest: CancelChatRequestDto,
  ): void {
    const backendSocket = this.connectionMap.get(sessionId);

    // Check connection status before sending
    if (!backendSocket || backendSocket.readyState !== WebSocket.OPEN) {
      if (this.reconnectionAttempts.has(sessionId)) {
        this.logger.warn(
          `Cannot send CANCEL_CHAT_REQUEST for session ${sessionId}: Backend is reconnecting.`,
        );
        this.notifyError(
          sessionId,
          StandardErrorCode.BACKEND_TEMPORARILY_UNAVAILABLE,
          'Cannot process request: Backend is reconnecting, please wait.',
          cancelRequest.request_id,
        );
      } else {
        this.logger.warn(
          `Cannot send CANCEL_CHAT_REQUEST for session ${sessionId}: Backend connection not open or not found.`,
        );
        this.notifyError(
          sessionId,
          StandardErrorCode.BACKEND_UNAVAILABLE,
          'Backend connection is unavailable.',
          cancelRequest.request_id,
        );
      }
      return;
    }

    // Construct the backend request format as C2S_CHAT with cancel: true
    const backendRequest: BackendChatRequestDto = {
      type: BackendMessageType.ChatRequest,
      user_id: userId,
      session_id: sessionId,
      request_id: cancelRequest.request_id ?? '',
      timestamp: new Date().toISOString(),
      payload: {
        cancel: true,
      },
    };

    this.logger.debug(
      `Sending C2S_CHAT (cancel) to backend for session ${sessionId}: ${JSON.stringify(backendRequest)}`,
    );
    try {
      backendSocket.send(JSON.stringify(backendRequest));
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Error sending cancel request to backend: ${errorMessage}`,
        error instanceof Error ? error.stack : '',
        'BackendWsService',
      );
      this.notifyError(
        sessionId,
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        `Failed to send cancel request: ${errorMessage}`,
        cancelRequest.request_id,
      );
    }
  }

  sendSettingsRequest(
    sessionId: string,
    userId: string,
    settingsRequest: SettingsRequestDto,
  ): void {
    const backendSocket = this.connectionMap.get(sessionId);

    // Check connection status before sending
    if (!backendSocket || backendSocket.readyState !== WebSocket.OPEN) {
      if (this.reconnectionAttempts.has(sessionId)) {
        this.logger.warn(
          `Cannot send SET_SETTINGS for session ${sessionId}: Backend is reconnecting.`,
        );
        this.notifyError(
          sessionId,
          StandardErrorCode.BACKEND_TEMPORARILY_UNAVAILABLE,
          'Cannot process request: Backend is reconnecting, please wait.',
          settingsRequest.request_id,
        );
      } else {
        this.logger.warn(
          `Cannot send SET_SETTINGS for session ${sessionId}: Backend connection not open or not found.`,
        );
        this.notifyError(
          sessionId,
          StandardErrorCode.BACKEND_UNAVAILABLE,
          'Backend connection is unavailable.',
          settingsRequest.request_id,
        );
      }
      return;
    }

    // Construct the backend request format
    const backendRequest: BackendSettingsRequestDto = {
      type: BackendMessageType.SettingsRequest,
      user_id: userId,
      session_id: sessionId,
      request_id: settingsRequest.request_id ?? '',
      timestamp: new Date().toISOString(),
      payload: settingsRequest.settings,
    };

    this.logger.debug(
      `Sending SET_SETTINGS to backend for session ${sessionId}: ${JSON.stringify(backendRequest)}`,
    );
    try {
      backendSocket.send(JSON.stringify(backendRequest));
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Error sending settings request to backend: ${errorMessage}`,
        error instanceof Error ? error.stack : '',
        'BackendWsService',
      );
      this.notifyError(
        sessionId,
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        `Failed to send settings request: ${errorMessage}`,
        settingsRequest.request_id,
      );
    }
  }

  sendSessionNameRequest(
    sessionId: string,
    userId: string,
    sessionNameRequest: SessionNameRequestDto,
  ): void {
    const backendSocket = this.connectionMap.get(sessionId);

    // Check connection status before sending
    if (!backendSocket || backendSocket.readyState !== WebSocket.OPEN) {
      if (this.reconnectionAttempts.has(sessionId)) {
        this.logger.warn(
          `Cannot send GENERATE_SESSION_NAME for session ${sessionId}: Backend is reconnecting.`,
        );
        this.notifyError(
          sessionId,
          StandardErrorCode.BACKEND_TEMPORARILY_UNAVAILABLE,
          'Cannot process request: Backend is reconnecting, please wait.',
          sessionNameRequest.request_id,
        );
      } else {
        this.logger.warn(
          `Cannot send GENERATE_SESSION_NAME for session ${sessionId}: Backend connection not open or not found.`,
        );
        this.notifyError(
          sessionId,
          StandardErrorCode.BACKEND_UNAVAILABLE,
          'Backend connection is unavailable.',
          sessionNameRequest.request_id,
        );
      }
      return;
    }

    // Construct the backend request format
    const backendRequest: BackendSessionNameRequestDto = {
      type: BackendMessageType.SessionNameRequest,
      user_id: userId,
      session_id: sessionId,
      request_id: sessionNameRequest.request_id ?? '',
      timestamp: new Date().toISOString(),
    };

    this.logger.debug(
      `Sending GENERATE_SESSION_NAME to backend for session ${sessionId}: ${JSON.stringify(backendRequest)}`,
    );
    try {
      backendSocket.send(JSON.stringify(backendRequest));
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Error sending session name request to backend: ${errorMessage}`,
        error instanceof Error ? error.stack : '',
        'BackendWsService',
      );
      this.notifyError(
        sessionId,
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        `Failed to send session name request: ${errorMessage}`,
        sessionNameRequest.request_id,
      );
    }
  }

  sendFeedbackRequest(
    sessionId: string,
    userId: string,
    feedbackRequest: FeedbackRequestDto,
  ): void {
    const backendSocket = this.connectionMap.get(sessionId);

    // Check connection status before sending
    if (!backendSocket || backendSocket.readyState !== WebSocket.OPEN) {
      if (this.reconnectionAttempts.has(sessionId)) {
        this.logger.warn(
          `Cannot send SEND_FEEDBACK for session ${sessionId}: Backend is reconnecting.`,
        );
        this.notifyError(
          sessionId,
          StandardErrorCode.BACKEND_TEMPORARILY_UNAVAILABLE,
          'Cannot process request: Backend is reconnecting, please wait.',
          feedbackRequest.request_id,
        );
      } else {
        this.logger.warn(
          `Cannot send SEND_FEEDBACK for session ${sessionId}: Backend connection not open or not found.`,
        );
        this.notifyError(
          sessionId,
          StandardErrorCode.BACKEND_UNAVAILABLE,
          'Backend connection is unavailable.',
          feedbackRequest.request_id,
        );
      }
      return;
    }

    // Construct the backend request format
    const backendRequest: BackendFeedbackRequestDto = {
      type: BackendMessageType.FeedbackRequest,
      user_id: userId,
      session_id: sessionId,
      request_id: feedbackRequest.request_id ?? '',
      timestamp: new Date().toISOString(),
      payload: feedbackRequest.payload,
    };

    this.logger.debug(
      `Sending SEND_FEEDBACK to backend for session ${sessionId}: ${JSON.stringify(backendRequest)}`,
    );
    try {
      backendSocket.send(JSON.stringify(backendRequest));
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Error sending feedback request to backend: ${errorMessage}`,
        error instanceof Error ? error.stack : '',
        'BackendWsService',
      );
      this.notifyError(
        sessionId,
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        `Failed to send feedback request: ${errorMessage}`,
        feedbackRequest.request_id,
      );
    }
  }

  // --- Connection Management Methods ---

  /**
   * Intentionally closes the backend WebSocket connection for a session.
   * Prevents automatic reconnection attempts for this session.
   *
   * @param sessionId The session ID to close the connection for.
   */
  closeConnection(sessionId: string): void {
    this.logger.log(
      `Explicitly closing connection for session ${sessionId}...`,
    );

    // Prevent reconnection attempts
    this.cancelReconnection(sessionId);
    this.clientCallbacks.delete(sessionId); // Remove callback to signal no reconnect needed

    const socket = this.connectionMap.get(sessionId);
    if (socket) {
      this.stopHeartbeat(sessionId); // Stop heartbeats before closing
      if (
        socket.readyState === WebSocket.OPEN ||
        socket.readyState === WebSocket.CONNECTING
      ) {
        socket.close(1000, 'Client session ended'); // Use normal closure code
      } else {
        // If socket is already closing/closed, ensure state is cleaned up
        this.cleanupConnectionState(sessionId);
      }
      // Remove from map immediately after initiating close or confirming state
      this.connectionMap.delete(sessionId);
    } else {
      this.logger.warn(
        `Attempted to close non-existent connection for session ${sessionId}. Ensuring cleanup.`,
      );
      this.cleanupConnectionState(sessionId); // Ensure maps are clear even if socket was missing
    }
    this.logger.log(`Connection closure initiated for session ${sessionId}.`);
  }

  /**
   * Cleans up only the state related to an active connection (socket, heartbeat, pong timeout).
   * Does NOT remove connection arguments or cancel reconnection timers, as those might be needed.
   * Should be called when a connection attempt fails or socket becomes unusable *before* 'close'.
   */
  private cleanupConnectionState(sessionId: string): void {
    this.logger.debug(
      `Cleaning up connection state for session ${sessionId}...`,
    );
    this.stopHeartbeat(sessionId); // Stops heartbeat timer and clears pong timeout
    this.connectionMap.delete(sessionId); // Remove socket reference
  }

  /**
   * Performs a full cleanup for a session, removing all associated state,
   * including connection arguments and cancelling any pending reconnections.
   * Called after a final reconnection failure or normal closure.
   */
  private cleanupSessionCompletely(sessionId: string): void {
    this.logger.log(`Performing complete cleanup for session ${sessionId}...`);
    this.cancelReconnection(sessionId); // Ensure no pending reconnection timers
    this.stopHeartbeat(sessionId); // Ensure heartbeat/pong timers are stopped
    this.connectionMap.delete(sessionId); // Remove socket ref if somehow still present
    this.clientCallbacks.delete(sessionId); // Deprecated if using global handler, but good practice
    this.reconnectionAttempts.delete(sessionId); // Clear attempt count
    this.logger.log(`Complete cleanup finished for session ${sessionId}.`);
  }

  // --- Utility Methods ---

  private parseMessageData(data: any): string {
    if (Buffer.isBuffer(data)) {
      return data.toString('utf-8');
    } else if (data instanceof ArrayBuffer) {
      return Buffer.from(data).toString('utf-8');
    } else if (
      Array.isArray(data) &&
      data.every((item) => Buffer.isBuffer(item))
    ) {
      return Buffer.concat(data).toString('utf-8');
    } else if (typeof data === 'string') {
      return data;
    } else {
      this.logger.warn(
        'Received unexpected data type from backend websocket',
        typeof data,
      );
      return JSON.stringify({
        type: 'unknown',
        message: 'Unhandled data type',
      });
    }
  }

  /** Notifies the UI gateway (via global handler) about connection status changes */
  private notifyBackendStatus(
    sessionId: string,
    status: BackendConnectionStatus,
    message?: string,
  ): void {
    this.logger.log(
      `Notifying UI Gateway: Session ${sessionId} status changed to ${status}${message ? ` (${message})` : ''}`,
    );
    const statusMessage: BackendStatusMessageDto = {
      type: 'BACKEND_CONNECTION_STATUS',
      session_id: sessionId,
      status: status,
      message: message || `Backend connection status: ${status}`,
    };
    if (this.globalMessageHandler) {
      this.globalMessageHandler(sessionId, statusMessage);
    } else {
      // Fallback to session callback if global handler isn't set (less ideal)
      const callback = this.clientCallbacks.get(sessionId);
      if (callback) {
        callback(statusMessage);
      } else {
        this.logger.warn(
          `Cannot notify status ${status} for session ${sessionId}: No handler found.`,
        );
      }
    }
  }

  /** Notifies the UI gateway (via global handler) about specific errors */
  private notifyError(
    sessionId: string,
    code: StandardErrorCode,
    message: string,
    requestId?: string,
  ): void {
    const errorPayload: ErrorMessageDto = {
      type: 'ERROR_MESSAGE',
      session_id: sessionId,
      request_id: requestId,
      code: code,
      message: message,
    };
    if (this.globalMessageHandler) {
      this.globalMessageHandler(sessionId, errorPayload);
    } else {
      // Fallback to session callback
      const callback = this.clientCallbacks.get(sessionId);
      if (callback) {
        callback(errorPayload);
      } else {
        this.logger.warn(
          `Cannot notify error ${code} for session ${sessionId}: No handler found.`,
        );
      }
    }
  }
}
