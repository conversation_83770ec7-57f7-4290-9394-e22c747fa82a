import { Injectable, Logger } from '@nestjs/common';
import { ErrorDto, UiOutputMessage } from '../dto/ui-output-message.dto';
import { StandardErrorCode } from '../dto/common.dto';
import { IBackendMessageHandler } from '../handlers/backend-message-handler.interface';
import { InitialSessionStateHandler } from '../handlers/initial-session-state.handler';
import { ChatUpdateHandler } from '../handlers/chat-update.handler';
import { InspirationStatusUpdateHandler } from '../handlers/inspiration-status-update.handler';
import { ErrorHandler } from '../handlers/error.handler';
import { GenericHandler } from '../handlers/generic.handler';
import { SettingsUpdateHandler } from '../handlers/settings-update.handler';
import { SessionNameUpdateHandler } from '../handlers/session-name-update.handler';
import { FeedbackUpdateHandler } from '../handlers/feedback-update.handler';

@Injectable()
export class BackendMessageTransformer {
  private readonly logger = new Logger(BackendMessageTransformer.name);
  private readonly handlers: IBackendMessageHandler[];

  constructor(
    initialSessionStateHandler: InitialSessionStateHandler,
    chatUpdateHandler: ChatUpdateHandler,
    inspirationStatusUpdateHandler: InspirationStatusUpdateHandler,
    errorHandler: ErrorHandler,
    genericHandler: GenericHandler,
    settingsUpdateHandler: SettingsUpdateHandler,
    sessionNameUpdateHandler: SessionNameUpdateHandler,
    feedbackUpdateHandler: FeedbackUpdateHandler,
  ) {
    this.handlers = [
      initialSessionStateHandler,
      chatUpdateHandler,
      inspirationStatusUpdateHandler,
      settingsUpdateHandler,
      sessionNameUpdateHandler,
      feedbackUpdateHandler,
      errorHandler,
      genericHandler,
    ];
  }

  /**
   * Transforms messages received from the backend service into the format
   * expected by UI clients by delegating to the appropriate handler.
   *
   * @param sessionId The session ID the message belongs to.
   * @param message The raw message object from the backend.
   * @returns A transformed message object for the UI, or null if the message should be suppressed.
   */
  async transform(
    sessionId: string,
    message: Record<string, unknown>,
  ): Promise<UiOutputMessage | null> {
    if (!message || typeof message !== 'object') {
      this.logger.error(
        `[${sessionId}] Invalid message format received from backend`,
        JSON.stringify(message),
      );
      return null;
    }

    this.logger.debug(
      `[${sessionId}] Transforming backend message via handlers: ${JSON.stringify(message).substring(0, 200)}...`,
    );

    const handler = this.handlers.find((h) => h.canHandle(message));

    if (!handler) {
      this.logger.error(
        `[${sessionId}] No suitable handler found for message! This is unexpected.`,
        message,
      );
      return {
        type: 'ERROR',
        session_id: sessionId,
        request_id:
          typeof message.request_id === 'string'
            ? message.request_id
            : undefined,
        error: {
          code: StandardErrorCode.INTERNAL_SERVER_ERROR,
          message: 'Failed to find handler for backend message',
        },
      } as ErrorDto;
    }

    try {
      return await handler.transform(sessionId, message);
    } catch (error) {
      this.logger.error(
        `[${sessionId}] Error during transformation by handler ${handler.constructor.name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined,
      );

      return {
        type: 'ERROR',
        session_id: sessionId,
        request_id:
          typeof message.request_id === 'string'
            ? message.request_id
            : undefined,
        error: {
          code: StandardErrorCode.INTERNAL_SERVER_ERROR,
          message: 'Failed to process backend message due to handler error',
        },
      } as ErrorDto;
    }
  }
}
