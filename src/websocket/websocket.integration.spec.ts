import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { WebSocketModule } from './websocket.module';
import { UiGateway } from './gateways/ui.gateway';
import { BackendWsService } from './services/backend-ws.service';
import { StandardErrorCode } from './dto/common.dto';
import { SupportedLanguage } from 'src/common/languages';
import { DetailedUserResponse } from './dto/common.dto';
import { UserService } from '../common/helpers/user-service';
import { IncomingMessage } from 'http';
import { SessionRepository } from '../sessions/session.repository';
import { SessionsService } from '../sessions/sessions.service';
import { FgaService } from '../auth/fga/fga.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

// Import types and classes needed for mocks and tests
import { WebSocketWithId } from './managers/client.manager';
import { ClientManager } from './managers/client.manager';
import { SessionManager } from './managers/session.manager';
import { ErrorHandlerService } from './services/error-handler.service';
import { BackendMessageTransformer } from './transformers/backend-message.transformer';
import { ChatRequestHandler } from './handlers/chat-request.handler';
import { CancelChatRequestHandler } from './handlers/cancel-chat-request.handler';
import { ChatRequestDto } from './dto/chat-request.dto';
import { CancelChatRequestDto } from './dto/cancel-chat-request.dto';

// Mock WebSocket
jest.mock('ws');

describe('WebSocketModule Integration', () => {
  let moduleRef: TestingModule | null = null;
  let uiGateway: UiGateway;

  // --- Simple Mocks for Dependencies ---
  let mockUserService: Partial<UserService>;
  let mockBackendService: Partial<BackendWsService>;
  let mockSessionRepository: Partial<SessionRepository>;
  let mockSessionsService: Partial<SessionsService>;
  let mockFgaService: Partial<FgaService>;
  let mockCacheManager: any;
  let mockClientManager: Partial<ClientManager>;
  let mockSessionManager: Partial<SessionManager>;
  let mockErrorHandlerService: Partial<ErrorHandlerService>;
  let mockMessageTransformer: Partial<BackendMessageTransformer>;
  let mockChatRequestHandler: Partial<ChatRequestHandler>;
  let mockCancelChatRequestHandler: Partial<CancelChatRequestHandler>;
  // Add other handlers if tests are added for them

  // Mock client - keep simple mock for interactions
  const mockClient = {
    send: jest.fn(),
    close: jest.fn(),
    readyState: 1, // WebSocket.OPEN
    on: jest.fn(),
    clientId: 'mock-client-id',
  } as unknown as jest.Mocked<WebSocketWithId>;

  // Mock user - keep for external UserService mock
  const mockUser: DetailedUserResponse = {
    id: 'test-user-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    username: 'testuser',
    display_picture_url: null,
    preferences: {
      preferred_detection_language: 'sigma',
    },
  };

  // Helper to create mock request
  const createMockRequest = (url: string): IncomingMessage => {
    // Create a mock IncomingMessage without using a real socket
    // This avoids type safety issues with Socket
    class MockIncomingMessage extends IncomingMessage {
      constructor(url: string, headers: Record<string, string>) {
        // @ts-expect-error - We're bypassing the socket requirement for testing
        super(null);
        this.url = url;
        this.headers = headers;
      }
    }

    return new MockIncomingMessage(url, { host: 'localhost:3000' });
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    mockClient.send.mockClear();
    mockClient.close.mockClear();
    mockClient.on.mockClear();

    // --- Define Simple Mock Objects ---
    const mockConfigService = {
      get: jest.fn((key: string): string | null => {
        switch (key) {
          case 'RULE_GENERATOR_WS':
            return 'ws://backend-server/ws';
          case 'DEFAULT_LANGUAGE':
            return 'sigma';
          default:
            return null;
        }
      }),
      getOrThrow: jest.fn((key: string): string => {
        const config: Record<string, string> = {
          FGA_WORKSPACE_ID: 'test-workspace',
          FGA_API_URL: 'http://fga.test',
          FGA_STORE_ID: 'test-store',
          FGA_API_TOKEN_ISSUER: 'test-issuer',
          FGA_API_AUDIENCE: 'test-audience',
          FGA_CLIENT_ID: 'test-client',
          FGA_CLIENT_SECRET: 'test-secret',
        };
        return config[key] || `mock-${key}`;
      }),
    };
    mockUserService = {
      getInternalUser: jest.fn().mockResolvedValue(mockUser),
    };
    mockBackendService = {
      connectToBackend: jest.fn(),
      closeConnection: jest.fn(),
      sendChatRequest: jest.fn(),
      sendCancelRequest: jest.fn(),
      sendInspirationRequest: jest.fn(),
      setClientGlobalHandler: jest.fn(),
    };
    mockSessionRepository = {
      getSession: jest.fn().mockResolvedValue({
        language: 'sigma',
        language_locked: false,
        id: 'test-session-id',
      }),
      // Add other methods if needed by tested code paths
      findById: jest.fn(),
      update: jest.fn(),
    };
    mockSessionsService = {
      getSessionById: jest
        .fn()
        .mockResolvedValue({ language: 'sigma', language_locked: false }),
      updateSessionLanguage: jest
        .fn()
        .mockResolvedValue({ language: 'sigma', language_locked: true }),
      // Add other methods if needed
    };
    mockFgaService = { checkTuple: jest.fn().mockResolvedValue(true) }; // Simplified
    mockCacheManager = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn().mockResolvedValue(undefined),
    };
    mockClientManager = {
      registerClient: jest.fn(),
      authenticateClient: jest.fn(),
      unregisterClient: jest.fn(),
      getClientData: jest.fn().mockReturnValue({
        userId: mockUser.id,
        sessionId: 'test-session-id',
        authenticated: true,
        status: 'ready',
      }),
      getClientSocket: jest.fn().mockReturnValue(mockClient),
      getAllClientIds: jest.fn(),
    };
    mockSessionManager = {
      addClientToSession: jest.fn(),
      removeClientFromSession: jest.fn(),
      getClientsInSession: jest
        .fn()
        .mockReturnValue(new Set([mockClient.clientId])),
    };
    mockErrorHandlerService = { sendErrorToClient: jest.fn() };
    mockMessageTransformer = { transform: jest.fn().mockResolvedValue(null) };
    mockChatRequestHandler = {
      canHandle: jest.fn((msg) => msg?.type === 'CHAT_REQUEST'),
      validate: jest
        .fn()
        .mockImplementation((msg) => Promise.resolve(msg as ChatRequestDto)),
      handle: jest.fn().mockImplementation(() => {
        // Simulate handler calling the backend service
        return mockBackendService.sendChatRequest!(
          'test-session-id',
          'test-user-id',
          {} as any,
        );
      }),
    };
    mockCancelChatRequestHandler = {
      canHandle: jest.fn((msg) => msg?.type === 'CANCEL_CHAT_REQUEST'),
      validate: jest
        .fn()
        .mockImplementation((msg) =>
          Promise.resolve(msg as CancelChatRequestDto),
        ),
      handle: jest.fn().mockImplementation(() => {
        // Simulate handler calling the backend service
        return mockBackendService.sendCancelRequest!(
          'test-session-id',
          'test-user-id',
          {} as any,
        );
      }),
    };

    // --- Override ALL Providers ---
    moduleRef = await Test.createTestingModule({
      imports: [WebSocketModule],
    })
      .overrideProvider(ConfigService)
      .useValue(mockConfigService)
      .overrideProvider(UserService)
      .useValue(mockUserService)
      .overrideProvider(BackendWsService)
      .useValue(mockBackendService)
      .overrideProvider(SessionRepository)
      .useValue(mockSessionRepository)
      .overrideProvider(SessionsService)
      .useValue(mockSessionsService)
      .overrideProvider(FgaService)
      .useValue(mockFgaService)
      .overrideProvider(CACHE_MANAGER)
      .useValue(mockCacheManager)
      // Override internal dependencies with simple mocks
      .overrideProvider(ClientManager)
      .useValue(mockClientManager)
      .overrideProvider(SessionManager)
      .useValue(mockSessionManager)
      .overrideProvider(ErrorHandlerService)
      .useValue(mockErrorHandlerService)
      .overrideProvider(BackendMessageTransformer)
      .useValue(mockMessageTransformer)
      .overrideProvider(ChatRequestHandler)
      .useValue(mockChatRequestHandler)
      .overrideProvider(CancelChatRequestHandler)
      .useValue(mockCancelChatRequestHandler)
      // Add overrides for InspirationRequestHandler, SetLanguageRequestHandler if tests require them
      .compile();

    // Get the gateway instance (now with all dependencies mocked)
    uiGateway = moduleRef.get<UiGateway>(UiGateway);

    // Mock server part needed by afterInit
    uiGateway.server = { on: jest.fn() } as any;
    uiGateway.afterInit();
  });

  afterEach(async () => {
    if (moduleRef) {
      await moduleRef.close();
      moduleRef = null;
    }
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(uiGateway).toBeDefined();
  });

  describe('Client connections', () => {
    it('should authenticate a client with valid token and session ID', async () => {
      const token = 'valid-token';
      const sessionId = 'test-session-id';
      const url = `ws://server/canvas-ws?token=${token}&session_id=${sessionId}`;
      const request = createMockRequest(url);

      await uiGateway.handleConnection(
        mockClient as unknown as WebSocketWithId,
        request,
      );

      // Assert external calls
      expect(mockUserService.getInternalUser).toHaveBeenCalledWith(
        `Bearer ${token}`,
      );
      expect(mockBackendService.connectToBackend).toHaveBeenCalledWith(
        sessionId,
        mockUser.id,
        expect.any(Function),
      );
      // Assert internal state setup via spies if necessary, e.g.:
      // const clientData = clientManager.getClientData(mockClient.clientId);
      // expect(clientData).toBeDefined();
      // expect(clientData?.status).toBe('ready');
    });

    it('should reject a client without token', async () => {
      const sessionId = 'test-session-id';
      const url = `ws://server/canvas-ws?session_id=${sessionId}`;
      const request = createMockRequest(url);

      await uiGateway.handleConnection(
        mockClient as unknown as WebSocketWithId,
        request,
      );

      expect(mockClient.close).toHaveBeenCalledWith(1008, expect.any(String));
    });

    it('should reject a client without session ID', async () => {
      const token = 'valid-token';
      const url = `ws://server/canvas-ws?token=${token}`;
      const request = createMockRequest(url);

      await uiGateway.handleConnection(
        mockClient as unknown as WebSocketWithId,
        request,
      );

      expect(mockClient.close).toHaveBeenCalledWith(1008, expect.any(String));
    });

    it('should reject a client with invalid token', async () => {
      // Arrange
      const sessionId = 'test-session-id';
      const invalidToken = 'invalid-token';
      const url = `ws://server/canvas-ws?token=${invalidToken}&session_id=${sessionId}`;
      const request = createMockRequest(url);
      // Setup mock rejection for this specific token
      (mockUserService.getInternalUser as jest.Mock).mockImplementation(
        (authHeader: string) => {
          if (authHeader === `Bearer ${invalidToken}`) {
            throw new Error('Authentication failed');
          }
          return mockUser;
        },
      );

      // Act
      // Put await back - Jest should handle the promise returned by handleConnection
      await uiGateway.handleConnection(
        mockClient as unknown as WebSocketWithId,
        request,
      );

      // Assert
      expect(mockUserService.getInternalUser).toHaveBeenCalledWith(
        `Bearer ${invalidToken}`,
      );
      expect(mockClient.close).toHaveBeenCalledWith(
        1008,
        'Authentication failed',
      );
    });

    it('should handle client disconnection', () => {
      const sessionId = 'test-session-id';
      // Arrange: Ensure unregisterClient is a mock and set its return value for this test
      (mockClientManager.unregisterClient as jest.Mock).mockReturnValueOnce({
        status: 'ready',
        sessionId: sessionId,
        userId: 'user-123',
        authenticated: true,
      });

      // Act
      uiGateway.handleDisconnect(mockClient as unknown as WebSocketWithId);

      // Assert
      expect(mockClientManager.unregisterClient).toHaveBeenCalledWith(
        mockClient.clientId,
      );
      expect(mockSessionManager.removeClientFromSession).toHaveBeenCalledWith(
        mockClient.clientId,
        sessionId,
      );
    });
  });

  describe('Chat Requests', () => {
    const sessionId = 'test-session-id';
    const chatRequestPayload: ChatRequestDto = {
      type: 'CHAT_REQUEST',
      session_id: sessionId,
      request_id: 'chat-req-1',
      prompt: 'Test prompt',
      artifacts: {
        detection_rule: {
          type: SupportedLanguage.SIGMA,
          content: 'test rule',
        },
      },
    };

    beforeEach(() => {
      // No connection needed here as dependencies are mocked
      // Reset specific mocks for clarity
      (mockChatRequestHandler.handle as jest.Mock).mockClear();
      (mockErrorHandlerService.sendErrorToClient as jest.Mock).mockClear();
      (mockBackendService.sendChatRequest as jest.Mock).mockClear();
    });

    it('should route CHAT_REQUEST to ChatRequestHandler', async () => {
      // Act
      await uiGateway['routeIncomingMessage'](
        mockClient as unknown as WebSocketWithId,
        JSON.stringify(chatRequestPayload),
      );

      // Assert mock handler was called
      expect(mockChatRequestHandler.canHandle).toHaveBeenCalledWith(
        chatRequestPayload,
      );
      expect(mockChatRequestHandler.validate).toHaveBeenCalledWith(
        chatRequestPayload,
      );
      expect(mockChatRequestHandler.handle).toHaveBeenCalledWith(
        mockClient,
        chatRequestPayload,
      );
      // Assert mock backend call (simulated within mock handle)
      expect(mockBackendService.sendChatRequest).toHaveBeenCalled();
      // Assert no error handled by mock error handler
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    it('should handle errors during chat request handling via ErrorHandler', async () => {
      // Arrange: Setup mock handler to throw an error
      const handlingError = new Error('Chat handle failed');
      (mockChatRequestHandler.handle as jest.Mock).mockRejectedValueOnce(
        handlingError,
      );

      // Act
      await uiGateway['routeIncomingMessage'](
        mockClient as unknown as WebSocketWithId,
        JSON.stringify(chatRequestPayload),
      );

      // Assert
      expect(mockChatRequestHandler.handle).toHaveBeenCalled();
      expect(mockErrorHandlerService.sendErrorToClient).toHaveBeenCalledWith(
        mockClient,
        'Error processing request', // Generic error from gateway catch block
        StandardErrorCode.INTERNAL_SERVER_ERROR,
        sessionId,
        chatRequestPayload.request_id,
      );
      // Ensure backend was not successfully called
      expect(mockBackendService.sendChatRequest).not.toHaveBeenCalled();
    });
    // Add test for validation error if needed
  });

  describe('Cancel Chat Requests', () => {
    const sessionId = 'test-session-id';
    const cancelRequestPayload: CancelChatRequestDto = {
      type: 'CANCEL_CHAT_REQUEST',
      session_id: sessionId,
      request_id: 'chat-req-to-cancel', // ID of the request to cancel
    };

    beforeEach(() => {
      (mockCancelChatRequestHandler.handle as jest.Mock).mockClear();
      (mockErrorHandlerService.sendErrorToClient as jest.Mock).mockClear();
      (mockBackendService.sendCancelRequest as jest.Mock).mockClear();
    });

    it('should route CANCEL_CHAT_REQUEST to CancelChatRequestHandler', async () => {
      // Act
      await uiGateway['routeIncomingMessage'](
        mockClient as unknown as WebSocketWithId,
        JSON.stringify(cancelRequestPayload),
      );

      // Assert mock handler
      expect(mockCancelChatRequestHandler.canHandle).toHaveBeenCalledWith(
        cancelRequestPayload,
      );
      expect(mockCancelChatRequestHandler.validate).toHaveBeenCalledWith(
        cancelRequestPayload,
      );
      expect(mockCancelChatRequestHandler.handle).toHaveBeenCalledWith(
        mockClient,
        cancelRequestPayload,
      );
      // Assert mock backend call (simulated within mock handle)
      expect(mockBackendService.sendCancelRequest).toHaveBeenCalled();
      expect(mockErrorHandlerService.sendErrorToClient).not.toHaveBeenCalled();
    });

    // Add error handling tests for cancel
  });

  // Add describe blocks for INSPIRATION_REQUEST, SET_LANGUAGE etc. following the same pattern

  describe('Backend Message Handling', () => {
    it.todo('should handle incoming backend messages and broadcast to clients');
  });
});
