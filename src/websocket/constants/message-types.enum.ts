/**
 * Defines the distinct types for messages received FROM the backend service.
 */
export enum BackendMessageType {
  Connection = 'S2C_CONNECTION',
  SettingsUpdate = 'S2C_SETTINGS',
  InspirationsUpdate = 'S2C_INSPIRATIONS',
  ChatUpdate = 'S2C_CHAT',
  SessionNameUpdate = 'S2C_SESSION_NAME',
  FeedbackResponse = 'S2C_FEEDBACK',
}

/**
 * Defines the distinct types for messages sent FROM the UI client TO the middleware.
 */
export enum UiInputMessageType {
  ChatRequest = 'CHAT_REQUEST',
  CancelChatRequest = 'CANCEL_CHAT_REQUEST',
  InspirationRequest = 'INSPIRATION_REQUEST',
  GetInspirationsRequest = 'GET_INSPIRATIONS_REQUEST', // Assuming based on handler names
  SetSettings = 'SET_SETTINGS',
  SessionNameRequest = 'GENERATE_SESSION_NAME',
  FeedbackRequest = 'SEND_FEEDBACK',
}

/**
 * Defines the distinct types for messages sent FROM the middleware TO the UI client.
 */
export enum UiOutputMessageType {
  InitialSessionState = 'INITIAL_SESSION_STATE',
  ChatUpdate = 'CHAT_UPDATE',
  InspirationStatusUpdate = 'INSPIRATION_STATUS_UPDATE',
  Error = 'ERROR', // Error generated by middleware
  LanguageUpdate = 'LANGUAGE_UPDATE',
  SessionIdResponse = 'SESSION_ID_RESPONSE',
  BackendMessage = 'BACKEND_MESSAGE', // Fallback type for GenericHandler
  SettingsUpdate = 'SETTINGS_UPDATE',
  FeedbackResponse = 'FEEDBACK_RESPONSE',
}
