import { Type } from 'class-transformer';
import {
  IsString,
  <PERSON><PERSON>ptional,
  IsUUID,
  IsEnum,
  IsArray,
  ValidateNested,
  IsNumber,
  Equals,
} from 'class-validator';
import { BaseUiMessageDto } from './ui-input-message.dto';

/**
 * Represents file metadata for a file inspiration
 */
export class FileMetadataDto {
  @IsString()
  original_file_name: string;

  @IsString()
  mime_type: string;

  @IsString()
  s3_path: string;

  @IsNumber()
  file_size: number;
}

/**
 * Represents URL metadata for a URL inspiration
 */
export class UrlMetadataDto {
  @IsString()
  url: string;
}

/**
 * Represents text metadata for a text inspiration
 */
export class TextMetadataDto {
  @IsString()
  text: string;
}

/**
 * Represents instruction metadata for an instruction inspiration
 */
export class InstructionMetadataDto {
  @IsString()
  instruction: string;
}

/**
 * Represents Mitre metadata for a Mitre inspiration
 */
export class MitreMetadata {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsString()
  type: string; // e.g., 'technique', 'tactic'

  @IsOptional()
  @IsArray()
  // Note: Validation for objects within array might require custom validator or @ValidateNested if mappings become a class
  mappings?: Array<{ [key: string]: any }>;

  @IsOptional()
  @IsArray()
  external_references?: Array<{ [key: string]: any }>;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  x_mitre_platforms?: string[];

  // Allow other arbitrary fields - validation for these is not straightforward with class-validator
  // Consider using a plain object type if strict validation isn't needed for extra fields
  // Or implement custom validation logic
  [key: string]: any;
}

/**
 * Enum for inspiration content types
 */
export enum InspirationContentType {
  FILE = 'FILE',
  URL = 'URL',
  TEXT = 'TEXT',
  INSTRUCTION = 'INSTRUCTION',
  MITRE = 'MITRE',
}

/**
 * Enum for inspiration actions
 */
export enum InspirationAction {
  ADD = 'add',
  DELETE = 'delete',
}

/**
 * Represents the content object within an inspiration request item
 */
export class InspirationContentDto {
  @IsEnum(InspirationContentType)
  type: InspirationContentType;

  @IsArray()
  @IsString({ each: true })
  tags: string[];

  @IsString()
  hash: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => FileMetadataDto)
  file_metadata?: FileMetadataDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => UrlMetadataDto)
  url_metadata?: UrlMetadataDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => TextMetadataDto)
  text_metadata?: TextMetadataDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => InstructionMetadataDto)
  instruction_metadata?: InstructionMetadataDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => MitreMetadata)
  mitre_metadata?: MitreMetadata;
}

/**
 * Represents a single inspiration item in a request from UI
 */
export class InspirationRequestItemDto {
  @IsString()
  id: string;

  @IsEnum(InspirationAction)
  action: InspirationAction;

  @IsOptional()
  @ValidateNested()
  @Type(() => InspirationContentDto)
  content?: InspirationContentDto;

  @IsString()
  @IsOptional()
  hash?: string; // For backward compatibility

  @IsString()
  @IsOptional()
  type?: string; // For backward compatibility
}

/**
 * Represents a single inspiration item in the backend format
 */
export class BackendInspirationItemDto {
  @IsString()
  id: string;

  @IsEnum(InspirationAction)
  action: InspirationAction;

  @IsString()
  @IsOptional()
  type?: string;

  @IsString()
  @IsOptional()
  url?: string;

  @IsString()
  @IsOptional()
  s3?: string;

  @IsString()
  @IsOptional()
  content?: string;

  @IsString()
  @IsOptional()
  hash?: string;
}

/**
 * Inspiration request sent from UI to DCM
 */
export class InspirationRequestDto extends BaseUiMessageDto {
  @Equals('INSPIRATION_REQUEST')
  readonly type = 'INSPIRATION_REQUEST';

  @IsString()
  @IsUUID(4, { message: 'session_id must be a valid UUID v4' })
  declare session_id: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => InspirationRequestItemDto)
  inspirations: InspirationRequestItemDto[];
}

/**
 * Inspiration request sent from DCM to backend
 */
export class BackendInspirationRequestDto {
  @Equals('C2S_INSPIRATIONS')
  readonly type = 'C2S_INSPIRATIONS';

  @IsString()
  session_id: string;

  @IsString()
  user_id: string;

  @IsString()
  request_id: string;

  @IsString()
  timestamp: string;

  payload: {
    inspirations: BackendInspirationItemDto[];
  };
}

// Maintain backward compatibility with interface types
export type InspirationRequestItem = InspirationRequestItemDto;
export type BackendInspirationItem = BackendInspirationItemDto;
export type FileMetadata = FileMetadataDto;
export type UrlMetadata = UrlMetadataDto;
export type TextMetadata = TextMetadataDto;
export type InstructionMetadata = InstructionMetadataDto;
