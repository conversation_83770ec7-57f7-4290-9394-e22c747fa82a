import {
  Inspiration,
  DetectionRule,
  Thought,
  DetectionCard,
  ChatResponseItem,
} from './common.dto';
import { UiOutputMessageType } from '../constants/message-types.enum';
import { CanvasSettings, CanvasError } from './common.dto';

/**
 * Base DTO for session-related messages sent TO the UI.
 */
interface BaseSessionMessageDto {
  session_id: string;
  request_id?: string;
  timestamp?: string;
}

/**
 * DTO for the initial state of a session sent to the UI.
 */
export interface InitialSessionStateDto extends BaseSessionMessageDto {
  type: UiOutputMessageType.InitialSessionState;
  session_name: string | null;
  language: string | null;
  language_locked: boolean;
  chat_history: ChatResponseItem[];
  inspirations?: Inspiration[];
  artifacts?: ChatUpdateArtifacts;
  settings?: CanvasSettings;
  supported_technologies?: string[];
  supported_security_tools?: string[];
  inspirations_token_limit?: number;
}

export interface ChatUpdateResponseArtifacts {
  thinking?: Thought;
  detection_cards?: DetectionCard[];
}

export interface ChatUpdateResponse {
  id: string;
  type?: string;
  content?: string;
  artifacts?: ChatUpdateResponseArtifacts;
  status?: string;
  done?: boolean;
}

export interface ChatUpdateArtifacts {
  detection_rule?: DetectionRule;
}

export interface ChatUpdateDto extends BaseSessionMessageDto {
  type: UiOutputMessageType.ChatUpdate;
  responses: ChatUpdateResponse[];
  artifacts?: ChatUpdateArtifacts;
  generated_session_name?: string;
  done?: boolean;
  status?: string;
  error?: CanvasError;
}
