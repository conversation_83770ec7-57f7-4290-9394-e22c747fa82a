import { IsString, IsUUID, Equals } from 'class-validator';
import { BaseUiMessageDto } from './ui-input-message.dto';

/**
 * Session name request sent from UI to DCM
 * Used to request generation of a session name
 */
export class SessionNameRequestDto extends BaseUiMessageDto {
  @Equals('GENERATE_SESSION_NAME')
  readonly type = 'GENERATE_SESSION_NAME';

  @IsString()
  @IsUUID(4, { message: 'session_id must be a valid UUID v4' })
  declare session_id: string;
}

/**
 * Session name request sent from DCM to backend
 * Used to request generation of a session name from the backend
 */
export class BackendSessionNameRequestDto {
  @Equals('C2S_SESSION_NAME')
  readonly type = 'C2S_SESSION_NAME';

  @IsString()
  session_id: string;

  @IsString()
  user_id: string;

  @IsString()
  request_id: string;

  @IsString()
  timestamp: string;
}
