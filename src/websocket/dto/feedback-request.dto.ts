import {
  IsString,
  IsUUI<PERSON>,
  IsBoolean,
  Is<PERSON>ptional,
  ValidateNested,
  Equals,
} from 'class-validator';
import { Type } from 'class-transformer';
import { BaseUiMessageDto } from './ui-input-message.dto';

/**
 * DTO for the payload of a feedback request
 */
export class FeedbackPayloadDto {
  @IsString()
  chat_id: string;

  @IsBoolean()
  like: boolean;

  @IsString()
  @IsOptional()
  feedback?: string;
}

/**
 * Feedback request sent from UI to DCM
 * Used to provide feedback on chat responses
 */
export class FeedbackRequestDto extends BaseUiMessageDto {
  @Equals('SEND_FEEDBACK')
  readonly type = 'SEND_FEEDBACK';

  @IsString()
  @IsUUID(4, { message: 'session_id must be a valid UUID v4' })
  declare session_id: string;

  @ValidateNested()
  @Type(() => FeedbackPayloadDto)
  payload: FeedbackPayloadDto;
}

/**
 * Feedback request sent from DCM to backend
 * Used to send feedback to the backend service
 */
export class BackendFeedbackRequestDto {
  @Equals('C2S_FEEDBACK')
  readonly type = 'C2S_FEEDBACK';

  @IsString()
  session_id: string;

  @IsString()
  user_id: string;

  @IsString()
  request_id: string;

  @IsString()
  timestamp: string;

  @ValidateNested()
  @Type(() => FeedbackPayloadDto)
  payload: FeedbackPayloadDto;
}
