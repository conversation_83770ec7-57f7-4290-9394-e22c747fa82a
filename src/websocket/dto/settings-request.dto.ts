import {
  IsString,
  IsOptional,
  IsUUID,
  IsArray,
  IsEnum,
  ValidateNested,
  Equals,
} from 'class-validator';
import { Type } from 'class-transformer';
import { BaseUiMessageDto } from './ui-input-message.dto';
import { SupportedLanguage } from 'src/common/languages';

/**
 * DTO for the payload of a settings request
 */
export class SettingsPayloadDto {
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  technologies?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  security_tools?: string[];

  @IsEnum(['sigma', 'kql', 'spl', 'yara'], {
    message: 'Detection language must be one of: sigma, kql, spl, yara',
  })
  @IsOptional()
  detection_language?: SupportedLanguage;

  @IsEnum(['low', 'medium', 'high'], {
    message: 'Detection verbosity must be one of: low, medium, high',
  })
  @IsOptional()
  detection_verbosity?: string;

  @IsEnum(['low', 'medium', 'high'], {
    message: 'Detection FP sensitivity must be one of: low, medium, high',
  })
  @IsOptional()
  detection_fp_sensitivity?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  custom_instructions?: string[];

  @IsEnum(['low', 'medium', 'high'], {
    message: 'Quality must be one of: low, medium, high',
  })
  @IsOptional()
  quality?: string;

  @IsString()
  @IsOptional()
  timezone?: string;
}

export class SettingsRequestDto extends BaseUiMessageDto {
  @Equals('SET_SETTINGS')
  readonly type = 'SET_SETTINGS';

  @IsString()
  @IsUUID(4, { message: 'session_id must be a valid UUID v4' })
  declare session_id: string;

  @ValidateNested()
  @Type(() => SettingsPayloadDto)
  settings: SettingsPayloadDto;
}

/**
 * Settings request sent from DCM to backend
 * Used to update session settings including technologies, security tools, etc.
 */
export class BackendSettingsRequestDto {
  @Equals('C2S_SETTINGS')
  readonly type = 'C2S_SETTINGS';

  @IsString()
  session_id: string;

  @IsString()
  user_id: string;

  @IsString()
  request_id: string;

  @IsString()
  timestamp: string;

  @ValidateNested()
  @Type(() => SettingsPayloadDto)
  payload: SettingsPayloadDto;
}
