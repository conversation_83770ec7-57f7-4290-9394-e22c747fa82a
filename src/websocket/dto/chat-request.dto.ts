import {
  IsString,
  IsOptional,
  IsUUID,
  IsArray,
  IsBoolean,
  ValidateNested,
  Equals,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ArtifactsDto } from './common.dto';
import { BaseUiMessageDto } from './ui-input-message.dto';
import { SupportedLanguage } from 'src/common/languages';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Chat request sent from UI to DCM
 * The first time this message is processed for a session,
 * the session's language will be locked by the DCM
 *
 * The request_id is optional and will be generated by the server
 * if not provided by the client
 */
export class ChatRequestDto extends BaseUiMessageDto {
  @Equals('CHAT_REQUEST')
  readonly type = 'CHAT_REQUEST';

  @IsString()
  @IsUUID(4, { message: 'session_id must be a valid UUID v4' })
  declare session_id: string;

  @IsString()
  @IsOptional()
  prompt?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  inspiration_ids?: string[];

  @ValidateNested()
  @Type(() => ArtifactsDto)
  @IsOptional()
  artifacts?: ArtifactsDto;

  @IsBoolean()
  @IsOptional()
  generate_session_name?: boolean;

  @IsEnum(['sigma', 'kql', 'spl', 'yara'], {
    message: 'Language must be one of: sigma, kql, spl, yara',
  })
  @IsOptional()
  language?: SupportedLanguage;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description:
      'The timezone to use for the session in the format of an IANA timezone identifier',
    example: 'America/New_York',
  })
  timezone?: string;
}

export class BackendChatRequestDto {
  @Equals('C2S_CHAT')
  readonly type = 'C2S_CHAT';

  @IsString()
  session_id: string;

  @IsString()
  user_id: string;

  @IsString()
  request_id: string;

  @IsString()
  timestamp: string;

  payload: {
    prompt?: string;
    artifacts?: ArtifactsDto;
    cancel?: boolean;
  };

  @IsBoolean()
  @IsOptional()
  cancel?: boolean;
}
