import {
  CanvasError,
  CanvasSettings,
  DetectionCard,
  Inspiration,
  Thought,
  DetectionRule,
} from './common.dto';
import { BackendMessageType } from '../constants/message-types.enum';

export interface BackendBaseMessage {
  timestamp: string;
  request_id?: string;
  type?: BackendMessageType;
  error?: CanvasError;
}

export interface BackendResponseArtifacts {
  detection_cards?: DetectionCard[];
  thought?: Thought;
}

export interface BackendResponse {
  id: string;
  type?: string;
  content?: string;
  artifacts?: BackendResponseArtifacts;
  error?: CanvasError;
}

export interface BackendMessage {
  id: string;
  prompt: string;
  inspirations: string[];
  responses: BackendResponse[];
  like?: boolean;
}

export interface BackendConnectionPayload {
  inspirations_token_limit: number;
  technologies: string[];
  security_tools: string[];
  settings: CanvasSettings;
  inspirations?: Inspiration[];
  messages?: BackendMessage[];
  artifacts?: {
    detection_rule: DetectionRule;
  };
}

/**
 * Main connection message interface following the new specification
 */
export interface BackendConnectionMessage extends BackendBaseMessage {
  type?: BackendMessageType.Connection;
  payload?: BackendConnectionPayload;
}

export interface BackendSettingsMessage extends BackendBaseMessage {
  type?: BackendMessageType.SettingsUpdate;
  payload?: {
    success: boolean;
  };
}

export interface BackendSessionNameMessage extends BackendBaseMessage {
  type?: BackendMessageType.SessionNameUpdate;
  payload?: {
    session_name: string;
  };
}

export interface BackendInspirationsResponse {
  inspirations: Inspiration[];
}

export interface BackendInspirationsMessage extends BackendBaseMessage {
  type?: BackendMessageType.InspirationsUpdate;
  payload?: BackendInspirationsResponse;
}

export interface BackendChatResponse {
  id: string;
  type?: string;
  status?: string;
  content?: string;
  replace_content?: boolean;
  artifacts?: BackendResponseArtifacts;
  error?: CanvasError;
  done?: boolean;
}

export interface BackendChatMessage extends BackendBaseMessage {
  type?: BackendMessageType.ChatUpdate;
  payload?: {
    status: string;
    responses: BackendChatResponse[];
    artifacts?: {
      detection_rule?: DetectionRule;
    };
    done?: boolean;
  };
}

export interface BackendFeedbackMessage extends BackendBaseMessage {
  type?: BackendMessageType.FeedbackResponse;
  payload?: {
    success: boolean;
  };
}

export interface BackendGenericMessage extends BackendBaseMessage {
  [key: string]: any; // Allow any other properties
}
