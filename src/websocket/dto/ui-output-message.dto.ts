import { StandardErrorCode, Inspiration } from './common.dto';
import { SupportedLanguage } from 'src/common/languages';
import { InitialSessionStateDto, ChatUpdateDto } from './session-message.dto';
import { UiOutputMessageType } from '../constants/message-types.enum';

/**
 * Base interface for all messages sent TO the UI WebSocket client.
 */
export interface BaseUiOutputMessage {
  type: UiOutputMessageType | string;
  session_id: string;
  request_id?: string;
  timestamp?: string;
}

/**
 * Represents an error message sent to the client.
 */
export interface ErrorDto extends BaseUiOutputMessage {
  type: UiOutputMessageType.Error;
  error: {
    code: StandardErrorCode | string;
    message: string;
  };
}

/**
 * Represents the response after a SET_LANGUAGE request.
 */
export interface LanguageUpdateDto extends BaseUiOutputMessage {
  type: UiOutputMessageType.LanguageUpdate;
  language: SupportedLanguage;
  language_locked: boolean;
}

/**
 * Represents the response when a new session ID is generated.
 */
export interface SessionIdResponseDto extends BaseUiOutputMessage {
  type: UiOutputMessageType.SessionIdResponse;
  new_session_id: string;
}

/**
 * Represents the status update for inspirations generation.
 */
export interface InspirationStatusUpdateDto extends BaseUiOutputMessage {
  type: UiOutputMessageType.InspirationStatusUpdate;
  inspirations: Inspiration[];
}

/**
 * Union type representing all possible messages sent from the middleware to the UI client.
 */
export type UiOutputMessage =
  | InitialSessionStateDto
  | ChatUpdateDto
  | ErrorDto
  | LanguageUpdateDto
  | SessionIdResponseDto
  | InspirationStatusUpdateDto
  | BaseUiOutputMessage;
