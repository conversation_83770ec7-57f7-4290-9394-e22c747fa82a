/**
 * Common DTO interfaces used across multiple WebSocket message types
 */

import { Type } from 'class-transformer';
import {
  IsString,
  IsBoolean,
  IsOptional,
  IsEnum,
  ValidateNested,
} from 'class-validator';
import { SupportedLanguage } from 'src/common/languages';

export interface CanvasError {
  code: number;
  message: string;
}

export interface CanvasSettings {
  technologies?: string[];
  security_tools?: string[];
  detection_language?: SupportedLanguage;
  detection_verbosity?: string;
  detection_fp_sensitivity?: string;
  custom_instructions?: string[];
  quality?: string;
}

export interface DetectionRule {
  id: string;
  type?: SupportedLanguage;
  title?: string;
  content?: string;
  status?: string;
  replace_content?: boolean;
  error?: CanvasError;
  done?: boolean;
}

export interface DetectionCard {
  id: string;
  inspiration_id?: string;
  title?: string;
  why_now?: string;
  pyramid_of_pain?: string[];
  mitre_ttps?: string[];
  iocs?: string[];
  threat_pattern?: string;
}

export interface Thought {
  id: string;
  status: string;
  content: string;
  elapsed_time: number;
  replace_content: boolean;
  error?: CanvasError;
  done: boolean;
}

export interface Inspiration {
  id: string;
  type: 'text' | 'url' | 'file' | 'instruction' | 'pdf' | 'image';
  tokens: number;
  success: boolean;
  error?: InspirationError | null;
  hash?: string;
}

export interface InspirationError {
  code: number | string;
  message: string;
}

export interface ChatResponseItem {
  id: string;
  type?: string;
  status?: string;
  content?: string;
  replace_content?: boolean;
  artifacts?: {
    detection_card?: DetectionCard;
    thought?: Thought;
  };
  done?: boolean;
  error?: CanvasError;
}

export interface ChatResponse {
  type: 'CHAT_RESPONSE';
  request_id: string;
  status: string;
  responses: ChatResponseItem[];
  artifacts?: {
    detection_rule?: DetectionRule;
  };
  done?: boolean;
  error?: CanvasError;
}

export enum StandardErrorCode {
  INVALID_REQUEST = 'INVALID_REQUEST',
  SESSION_NOT_FOUND = 'SESSION_NOT_FOUND',
  BACKEND_UNAVAILABLE = 'BACKEND_UNAVAILABLE',
  BACKEND_TEMPORARILY_UNAVAILABLE = 'BACKEND_TEMPORARILY_UNAVAILABLE',
  LLM_TIMEOUT = 'LLM_TIMEOUT',
  TOKEN_LIMIT_EXCEEDED = 'TOKEN_LIMIT_EXCEEDED',
  INSPIRATION_PROCESSING_FAILED = 'INSPIRATION_PROCESSING_FAILED',
  INSPIRATION_INVALID = 'INSPIRATION_INVALID',
  CANCEL_FAILURE = 'CANCEL_FAILURE',
  UNAUTHORIZED = 'UNAUTHORIZED',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  LANGUAGE_NOT_SET = 'LANGUAGE_NOT_SET',
  LANGUAGE_LOCKED = 'LANGUAGE_LOCKED',
}

/**
 * Detailed user information returned from the authentication service
 */
export interface DetailedUserResponse {
  id: string;
  username: string | null;
  email: string;
  firstName: string | null;
  lastName: string | null;
  display_picture_url: string | null;
  preferences: {
    preferred_detection_language: string | null;
  };
}

// --- DTO Classes with Validation ---

export class DetectionRuleDto {
  @IsEnum(SupportedLanguage, {
    message: `Detection rule type must be one of: ${Object.values(SupportedLanguage).join(', ')}`,
  })
  @IsOptional()
  type?: SupportedLanguage;

  @IsString()
  @IsOptional()
  content?: string;

  @IsBoolean()
  @IsOptional()
  done?: boolean;

  @IsString()
  @IsOptional()
  id?: string;
}

export class DetectionCardDto {
  @IsString()
  id: string;
}

export class ArtifactsDto {
  @ValidateNested()
  @Type(() => DetectionRuleDto)
  @IsOptional()
  detection_rule?: DetectionRuleDto;

  @ValidateNested()
  @Type(() => DetectionCardDto)
  @IsOptional()
  detection_cards?: DetectionCardDto[];
}
