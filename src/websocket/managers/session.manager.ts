import { Injectable, Logger } from '@nestjs/common';
import { SessionRepository } from '../../sessions/session.repository';
import { ClientManager } from './client.manager';
import { BackendWsService } from '../services/backend-ws.service';

@Injectable()
export class SessionManager {
  private readonly logger = new Logger(SessionManager.name);
  // Map sessionId to a Set of clientIds
  private readonly sessionToClients = new Map<string, Set<string>>();
  // Map sessionId to streaming rule state
  private readonly sessionStreamingRuleState = new Map<string, boolean>();

  constructor(
    private readonly clientManager: ClientManager, // Inject ClientManager
    private readonly sessionRepository: SessionRepository, // Inject SessionRepository
    private readonly backendWsService: BackendWsService, // Needed for closing connections
  ) {}

  /**
   * Associates a client with a specific session.
   *
   * @param clientId The ID of the client.
   * @param sessionId The ID of the session.
   */
  addClientToSession(clientId: string, sessionId: string): void {
    if (!this.sessionToClients.has(sessionId)) {
      this.sessionToClients.set(sessionId, new Set<string>());
      this.logger.log(`Session map created for new session: ${sessionId}`);
      // Potentially ensure backend connection here if this is the *first* client for the session
      // This logic might be better placed in handleConnection after successful auth
    }
    this.sessionToClients.get(sessionId)?.add(clientId);
    this.logger.log(`Client ${clientId} added to session ${sessionId}`);
  }

  /**
   * Removes a client from a session.
   * If it's the last client for the session, it also triggers closing the backend connection.
   *
   * @param clientId The ID of the client.
   * @param sessionId The ID of the session.
   */
  removeClientFromSession(clientId: string, sessionId: string): void {
    const sessionClientIds = this.sessionToClients.get(sessionId);
    const logPrefix = `[${clientId}] SessionManager -`;

    if (sessionClientIds) {
      sessionClientIds.delete(clientId);
      this.logger.log(`${logPrefix} Removed client from session ${sessionId}.`);

      if (sessionClientIds.size === 0) {
        this.logger.log(
          `${logPrefix} Last client for session ${sessionId}. Closing backend connection.`,
        );
        // Close backend WS connection for the session
        this.backendWsService.closeConnection(sessionId);
        // Remove the session entry from the map
        this.sessionToClients.delete(sessionId);
        // Clear streaming rule state for the session
        this.clearStreamingRuleState(sessionId);
        this.logger.log(`${logPrefix} Removed session ${sessionId} from map.`);
      } else {
        this.logger.log(
          `${logPrefix} Session ${sessionId} still has ${sessionClientIds.size} clients.`,
        );
      }
    } else {
      this.logger.warn(
        `${logPrefix} Session ${sessionId} not found in map when trying to remove client.`,
      );
    }
  }

  /**
   * Retrieves the set of client IDs associated with a session.
   *
   * @param sessionId The ID of the session.
   * @returns A Set of client IDs, or undefined if the session is not tracked.
   */
  getClientsInSession(sessionId: string): Set<string> | undefined {
    return this.sessionToClients.get(sessionId);
  }

  /**
   * Checks if a session is currently active (has clients).
   *
   * @param sessionId The ID of the session.
   * @returns True if the session has associated clients, false otherwise.
   */
  isSessionActive(sessionId: string): boolean {
    const clients = this.sessionToClients.get(sessionId);
    return !!clients && clients.size > 0;
  }

  /**
   * Gets the current streaming rule state for a session.
   *
   * @param sessionId The ID of the session.
   * @returns The streaming rule state, defaulting to false if not set.
   */
  getStreamingRuleState(sessionId: string): boolean {
    return this.sessionStreamingRuleState.get(sessionId) ?? false;
  }

  /**
   * Sets the streaming rule state for a session.
   *
   * @param sessionId The ID of the session.
   * @param isStreaming The new streaming rule state.
   */
  setStreamingRuleState(sessionId: string, isStreaming: boolean): void {
    this.sessionStreamingRuleState.set(sessionId, isStreaming);
    this.logger.debug(
      `[${sessionId}] Streaming rule state set to: ${isStreaming}`,
    );
  }

  /**
   * Clears the streaming rule state for a session.
   *
   * @param sessionId The ID of the session.
   */
  clearStreamingRuleState(sessionId: string): void {
    const wasStreaming = this.sessionStreamingRuleState.delete(sessionId);
    if (wasStreaming) {
      this.logger.debug(`[${sessionId}] Streaming rule state cleared`);
    }
  }
}
