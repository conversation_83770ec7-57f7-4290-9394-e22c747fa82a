import { Injectable, Logger } from '@nestjs/common';
import { WebSocket } from 'ws';
import { ClientData } from '../dto/client-data.dto';

// Re-define WebSocketWithId here or import from a shared types file if created
export interface WebSocketWithId extends WebSocket {
  clientId: string;
}

@Injectable()
export class ClientManager {
  private readonly logger = new Logger(ClientManager.name);

  // Map clientId to ClientData
  private readonly clientDataById = new Map<string, ClientData>();
  // Map clientId to WebSocket object
  private readonly clientIdToSocket = new Map<string, WebSocketWithId>();

  /**
   * Registers a new client upon connection.
   * Generates a unique client ID and stores the socket.
   * Initializes preliminary client data.
   *
   * @param client The WebSocket connection object.
   * @returns The generated unique client ID.
   */
  registerClient(client: WebSocketWithId): string {
    const clientId = client.clientId; // Assuming clientId is already set by handleConnection
    if (!clientId) {
      this.logger.error('Client registration attempted without a clientId!');
      // Handle this error appropriately, maybe close connection or throw
      throw new Error('Cannot register client without an ID');
    }

    this.clientIdToSocket.set(clientId, client);

    // Set preliminary data (auth happens later)
    const preliminaryClientData: Omit<ClientData, 'userId' | 'sessionId'> & {
      status: 'pending';
      authenticated: false;
    } = {
      authenticated: false,
      status: 'pending',
    };
    // Use type assertion carefully, ensuring required fields are added later
    this.clientDataById.set(clientId, preliminaryClientData as ClientData);

    this.logger.log(`Client registered with ID: ${clientId}`);
    return clientId;
  }

  /**
   * Updates client data upon successful authentication.
   *
   * @param clientId The ID of the client.
   * @param userId The authenticated user ID.
   * @param sessionId The session ID the client is joining.
   */
  authenticateClient(
    clientId: string,
    userId: string,
    sessionId: string,
  ): void {
    if (!this.clientIdToSocket.has(clientId)) {
      this.logger.warn(
        `Attempted to authenticate non-existent client ID: ${clientId}`,
      );
      return;
    }

    const finalClientData: ClientData = {
      userId: userId,
      sessionId: sessionId,
      authenticated: true,
      status: 'ready',
    };
    this.clientDataById.set(clientId, finalClientData);
    this.logger.log(
      `Client authenticated: ${clientId}, User: ${userId}, Session: ${sessionId}`,
    );
  }

  /**
   * Unregisters a client upon disconnection.
   * Removes the client's data and socket reference.
   *
   * @param clientId The ID of the client to unregister.
   * @returns The ClientData if found, otherwise undefined.
   */
  unregisterClient(clientId: string): ClientData | undefined {
    const clientData = this.clientDataById.get(clientId);
    const socket = this.clientIdToSocket.get(clientId);

    if (socket && socket.readyState === WebSocket.OPEN) {
      // Optionally close the socket if it's still open during unregistration
      // socket.close();
    }

    this.clientDataById.delete(clientId);
    this.clientIdToSocket.delete(clientId);

    if (clientData) {
      this.logger.log(
        `Client unregistered: ${clientId}, Status was: ${clientData.status}`,
      );
    } else {
      this.logger.warn(`Attempted to unregister unknown client: ${clientId}`);
    }
    return clientData;
  }

  /**
   * Retrieves the client data for a given client ID.
   *
   * @param clientId The ID of the client.
   * @returns The ClientData object or undefined if not found.
   */
  getClientData(clientId: string): ClientData | undefined {
    return this.clientDataById.get(clientId);
  }

  /**
   * Retrieves the WebSocket object for a given client ID.
   *
   * @param clientId The ID of the client.
   * @returns The WebSocketWithId object or undefined if not found.
   */
  getClientSocket(clientId: string): WebSocketWithId | undefined {
    return this.clientIdToSocket.get(clientId);
  }

  /**
   * Retrieves all client IDs currently registered.
   *
   * @returns An iterable of client IDs.
   */
  getAllClientIds(): IterableIterator<string> {
    return this.clientIdToSocket.keys();
  }

  /**
   * Gets the total number of registered clients
   *
   * @returns Number of clients
   */
  getClientCount(): number {
    return this.clientIdToSocket.size;
  }

  /**
   * Gets an array of all client IDs
   *
   * @returns Array of client IDs
   */
  getAllClientIdsArray(): string[] {
    return Array.from(this.clientIdToSocket.keys());
  }

  /**
   * Gets the user ID for a given session ID.
   * Loops through all clients and returns the user ID if the session ID matches.
   *
   * @param sessionId The ID of the session
   * @returns The user ID or undefined if not found
   */
  getUserIdForSession(sessionId: string): string | undefined {
    const clientIds = this.getAllClientIdsArray();
    for (const clientId of clientIds) {
      const clientData = this.getClientData(clientId);
      if (clientData?.sessionId === sessionId) {
        return clientData.userId;
      }
    }
    return undefined;
  }
}
