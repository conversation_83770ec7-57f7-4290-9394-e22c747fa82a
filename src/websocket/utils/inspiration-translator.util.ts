import {
  BackendInspirationRequestDto,
  BackendInspirationItemDto,
  InspirationRequestDto,
  InspirationRequestItemDto,
  MitreMetadata,
} from '../dto/inspiration-request.dto';
import { BackendMessageType } from '../services/backend-ws.service';

/**
 * Translates UI inspiration format to backend format
 * @param sessionId The session ID
 * @param userId The user ID
 * @param inspirationRequest The inspiration request from UI
 * @returns The translated request in backend format
 */
export function translateInspirationToBackendFormat(
  sessionId: string,
  userId: string,
  inspirationRequest: InspirationRequestDto,
): BackendInspirationRequestDto {
  return {
    type: BackendMessageType.InspirationRequest,
    session_id: sessionId,
    user_id: userId,
    request_id: inspirationRequest.request_id ?? '', // Request ID should've been set by the service already
    timestamp: new Date().toISOString(),
    payload: {
      inspirations: inspirationRequest.inspirations.map((item) =>
        translateInspirationItem(item),
      ),
    },
  };
}

/**
 * Translates a single inspiration item from UI format to backend format
 * @param item The inspiration item in UI format
 * @returns The inspiration item in backend format
 */
export function translateInspirationItem(
  item: InspirationRequestItemDto,
): BackendInspirationItemDto {
  const backendItem: BackendInspirationItemDto = {
    id: item.id,
    action: item.action,
    // Default type, might be overridden below
    type: item.content?.type?.toLowerCase() || item.type || 'unknown',
    // Copy hash from content or from the root item (for backward compatibility)
    hash: item.content?.hash || item.hash,
  };

  // Handle new format with complex content structure
  if (item.content) {
    // Update type using lowercase content.type and first tag if available
    if (item.content.tags && item.content.tags.length > 0) {
      backendItem.type = `${item.content.type.toLowerCase()}_${item.content.tags[0]}`;
    } else {
      backendItem.type = item.content.type.toLowerCase();
    }

    const contentTypeUpper = item.content.type.toUpperCase();

    // Handle different content types
    if (contentTypeUpper === 'FILE' && item.content.file_metadata) {
      // Fix to avoid double "s3://" prefix
      const s3Path = item.content.file_metadata.s3_path;
      backendItem.s3 = s3Path.startsWith('s3://') ? s3Path : `s3://${s3Path}`;
    } else if (contentTypeUpper === 'URL' && item.content.url_metadata) {
      backendItem.url = item.content.url_metadata.url;
    } else if (contentTypeUpper === 'TEXT' && item.content.text_metadata) {
      backendItem.content = item.content.text_metadata.text;
    } else if (
      contentTypeUpper === 'INSTRUCTION' &&
      item.content.instruction_metadata
    ) {
      backendItem.content = item.content.instruction_metadata.instruction;
    } else if (contentTypeUpper === 'MITRE') {
      // --- START: Handle MITRE ---
      backendItem.content = formatMitreContent(item.content.mitre_metadata);
      // Ensure type is set to 'mitre' (lowercase) if not tagged
      if (!(item.content.tags && item.content.tags.length > 0)) {
        backendItem.type = 'mitre';
      }
      // --- END: Handle MITRE ---
    }
  } else if (item.type) {
    // Handle legacy format if needed
    backendItem.type = item.type;
    if (typeof item.content === 'string') {
      backendItem.content = item.content;
    }
  }

  return backendItem;
}

// --- START: New Helper Function for MITRE ---
function formatMitreContent(
  metadata: MitreMetadata | undefined | null,
): string {
  if (!metadata) {
    return '[MITRE Metadata not provided]';
  }

  const lines: string[] = [];

  // Title
  lines.push(`MITRE Information (${metadata?.type || 'N/A'})`);
  lines.push('---'); // Separator

  // Core Fields
  lines.push(`ID: ${metadata?.id || 'N/A'}`);
  lines.push(`Name: ${metadata?.name || 'N/A'}`);
  lines.push(`Description: ${metadata?.description || 'N/A'}`);

  // Known Optional Fields (Examples)
  if (metadata?.x_mitre_platforms && metadata.x_mitre_platforms.length > 0) {
    lines.push(`Platforms: ${metadata.x_mitre_platforms.join(', ')}`);
  }
  if (
    metadata?.external_references &&
    metadata.external_references.length > 0
  ) {
    // Represent complex arrays/objects as JSON strings for simplicity
    try {
      lines.push(
        `External References: ${JSON.stringify(metadata.external_references)}`,
      );
    } catch (e) {
      lines.push(`External References: [Error converting to JSON]`);
      console.error('Error stringifying external_references:', e); // Log error
    }
  }

  // Other top-level fields (excluding known handled ones and mappings)
  const knownKeys = new Set([
    'id',
    'name',
    'description',
    'type',
    'mappings',
    'x_mitre_platforms',
    'external_references',
  ]);
  for (const key in metadata) {
    if (
      Object.prototype.hasOwnProperty.call(metadata, key) &&
      !knownKeys.has(key)
    ) {
      const value = metadata[key] as unknown; // Cast to unknown
      if (value !== undefined && value !== null) {
        lines.push(`${key}: ${JSON.stringify(value)}`); // Stringify complex values
      }
    }
  }

  // Mappings
  if (metadata?.mappings && metadata.mappings.length > 0) {
    lines.push('\n---'); // Separator
    lines.push('Mappings:');
    metadata.mappings.forEach((mapping, index) => {
      lines.push(`  Mapping ${index + 1}:`);
      for (const key in mapping) {
        if (Object.prototype.hasOwnProperty.call(mapping, key)) {
          const value = mapping[key] as unknown; // Cast to unknown
          if (value !== undefined && value !== null) {
            lines.push(`    ${key}: ${JSON.stringify(value)}`); // Stringify complex values
          }
        }
      }
    });
  }

  return lines.join('\n');
}
// --- END: New Helper Function for MITRE ---
