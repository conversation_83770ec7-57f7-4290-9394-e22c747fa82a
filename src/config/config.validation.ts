import { z } from 'zod';

// Helper function to create a preprocessed string schema
const safeString = () =>
  z.preprocess((val: unknown) => String(val), z.string());

// Helper function for string with trim and max length
const safeTrimmedString = (maxLength: number) =>
  safeString().pipe(z.string().trim().max(maxLength));

// Helper function for URL string
const safeUrl = () => safeString().pipe(z.string().url());

export const configValidationSchema = z.object({
  // Auth0 Configuration
  AUTH0_DOMAIN: safeTrimmedString(255),
  AUTH0_AUDIENCE: safeTrimmedString(255),
  AUTH0_ISSUER_URL: safeUrl(),
  AUTH0_WEB_CLIENT_ID: safeTrimmedString(255),
  // AUTH0_API_CLIENT_ID: z.string().trim().max(255).optional(), // <PERSON><PERSON> handles optionality differently if needed

  // JWT Security Settings
  AUTH0_JWT_CACHE_ENABLED: z
    .preprocess((val) => String(val).toLowerCase() === 'true', z.boolean())
    .default(true),
  AUTH0_JWT_RATE_LIMIT_ENABLED: z
    .preprocess((val) => String(val).toLowerCase() === 'true', z.boolean())
    .default(true),
  AUTH0_JWT_REQUESTS_PER_MINUTE: safeString()
    .transform((val) => parseInt(val, 10))
    .pipe(z.number().int().min(1).max(10))
    .default('5'),

  // Workspace Configuration
  FGA_WORKSPACE_ID: safeTrimmedString(255),

  // AWS Configuration
  AWS_REGION: safeTrimmedString(255),

  // DCM Configuration
  DYNAMODB_SESSIONS_TABLE: safeTrimmedString(255),
  DYNAMODB_ENDPOINT: safeUrl().optional(), // Optional, for local testing
  RULE_GENERATOR_WS: safeUrl(),

  // WebSocket Configuration
  WEBSOCKET_HEARTBEAT_INTERVAL_MS: safeString()
    .transform((val) => parseInt(val, 10))
    .pipe(z.number().int().positive())
    .default('10000'), // Default to 10 seconds

  // Backend WebSocket Configuration
  BACKEND_HEARTBEAT_INTERVAL_MS: safeString()
    .transform((val) => parseInt(val, 10))
    .pipe(z.number().int().positive())
    .default('30000'), // Default to 30 seconds

  BACKEND_PONG_TIMEOUT_MS: safeString()
    .transform((val) => parseInt(val, 10))
    .pipe(z.number().int().positive())
    .default('10000'), // Default to 10 seconds timeout

  // Other security settings
  PORT: safeString()
    .transform((val) => parseInt(val, 10))
    .pipe(z.number().int())
    .default('3000'),

  // Sentry Configuration
  SENTRY_DSN_URL: safeUrl().optional(),
  SENTRY_ENV: safeTrimmedString(255),

  CORS_ALLOWED_ORIGINS: safeString().default(''),
  CORS_ALLOWED_METHODS: safeString().default('GET,POST,PUT,DELETE,OPTIONS'),
  CORS_ALLOWED_HEADERS: safeString().default('*'),
  CORS_CREDENTIALS: safeString().default('false'),
  CORS_MAX_AGE: safeString().default('3600'),
});

// Optional: Infer a TypeScript type from the schema
// export type AppConfig = z.infer<typeof configValidationSchema>;
