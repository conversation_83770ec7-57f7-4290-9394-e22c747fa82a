import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { SessionRepository } from './session.repository';
import { Session } from './entities/session.entity';
import { PaginatedResponseDto } from './dto/pagination-response.dto';
import { v4 as uuidv4 } from 'uuid';
import { plainToInstance } from 'class-transformer';
import { SessionMetadataDto } from './dto/session-metadata.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SupportedLanguage } from 'src/common/languages';

@Injectable()
export class SessionsService {
  private readonly logger = new Logger(SessionsService.name);

  constructor(
    private readonly sessionRepository: SessionRepository,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Create a new session with default settings
   * @param userId The ID of the user creating the session
   * @returns The created session metadata
   */
  async createSession(userId: string): Promise<SessionMetadataDto> {
    try {
      const sessionId = uuidv4();
      const session = await this.sessionRepository.create({
        session_id: sessionId,
        user_id: userId,
        session_title: null,
        language: SupportedLanguage.SIGMA,
        language_locked: false,
        title_generated: false,
      });

      this.logger.log(`Created session ${sessionId} for user ${userId}`);
      return this.mapToMetadataDto(session);
    } catch (error: unknown) {
      this.logger.error(
        `Failed to create session for user ${userId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Get all sessions for a user with pagination
   * @param userId The ID of the user
   * @param page Page number (1-indexed)
   * @param pageSize Number of items per page
   * @param titleSearch Optional title search string
   * @returns Paginated list of session metadata
   */
  async getSessions(
    userId: string,
    page: number,
    pageSize: number,
    titleSearch?: string,
    bookmarked?: boolean,
    includeUntitled?: boolean, // If true, include sessions with null title
  ): Promise<PaginatedResponseDto<SessionMetadataDto>> {
    try {
      // Calculate DynamoDB limit and lastEvaluatedKey
      const limit = pageSize;
      let lastEvaluatedKey: Record<string, unknown> | undefined;

      // If not the first page, need to get the lastEvaluatedKey by querying previous pages
      // We do this because DynamoDB pagination doesn't support random access by page number
      if (page > 1) {
        let currentPage = 1;
        let currentResult = await this.sessionRepository.findByUser(
          userId,
          { limit: pageSize },
          titleSearch,
          bookmarked,
          includeUntitled,
        );

        while (currentPage < page - 1 && currentResult.lastEvaluatedKey) {
          currentResult = await this.sessionRepository.findByUser(
            userId,
            {
              limit: pageSize,
              lastEvaluatedKey: currentResult.lastEvaluatedKey,
            },
            titleSearch,
            bookmarked,
            includeUntitled,
          );
          currentPage++;
        }

        // If we've reached the page before the target page and have a lastEvaluatedKey
        if (currentPage === page - 1 && currentResult.lastEvaluatedKey) {
          lastEvaluatedKey = currentResult.lastEvaluatedKey;
        } else if (currentPage < page - 1) {
          // If we ran out of pages before reaching the target, return empty result
          return {
            data: [],
            meta: {
              page,
              page_size: pageSize,
              total_items: currentPage * pageSize,
              total_pages: currentPage,
            },
          };
        }
      }

      // Get the requested page
      const result = await this.sessionRepository.findByUser(
        userId,
        { limit, lastEvaluatedKey },
        titleSearch,
        bookmarked,
        includeUntitled,
      );

      // Get total count by continuing to query until no more results
      // Note: This approach can be inefficient with large datasets
      // A more efficient approach would be to maintain a count in a separate table
      let totalItems = result.items.length;
      let countLastEvaluatedKey = result.lastEvaluatedKey;

      // For smaller datasets, we can count the total number of items
      // For larger datasets, consider using an estimated count or a different approach
      const MAX_COUNT_PAGES = 10; // Limit the number of additional queries to avoid excessive DB usage
      let countPage = 1;

      while (countLastEvaluatedKey && countPage < MAX_COUNT_PAGES) {
        const countResult = await this.sessionRepository.findByUser(
          userId,
          { limit: 100, lastEvaluatedKey: countLastEvaluatedKey }, // Use larger page size for counting
          titleSearch,
          bookmarked,
          includeUntitled,
        );

        totalItems += countResult.items.length;
        countLastEvaluatedKey = countResult.lastEvaluatedKey;
        countPage++;
      }

      // If we still have more pages after MAX_COUNT_PAGES, make an educated guess
      if (countLastEvaluatedKey) {
        // Assuming a consistent distribution of items
        totalItems = Math.ceil(totalItems * 1.2); // Add 20% as a buffer
      }

      const totalPages = Math.ceil(totalItems / pageSize);

      // Map to DTOs
      const sessionDtos = result.items.map((session) =>
        this.mapToMetadataDto(session),
      );

      return {
        data: sessionDtos,
        meta: {
          page,
          page_size: pageSize,
          total_items: totalItems,
          total_pages: totalPages,
        },
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to get sessions for user ${userId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Get a session by ID
   * @param userId The ID of the user
   * @param sessionId The ID of the session
   * @returns The session metadata
   */
  async getSessionById(
    userId: string,
    sessionId: string,
  ): Promise<SessionMetadataDto> {
    try {
      const session = await this.sessionRepository.findById(userId, sessionId);

      if (!session) {
        throw new NotFoundException({
          errors: [
            {
              code: 'SESSION_NOT_FOUND',
              message: 'The requested canvas session was not found.',
              details: { session_id: sessionId },
            },
          ],
        });
      }

      return this.mapToMetadataDto(session);
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to get session ${sessionId} for user ${userId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Updates the title of a specific session for a user.
   * @param userId - The ID of the user owning the session.
   * @param sessionId - The ID of the session to update.
   * @param title - The new title for the session.
   * @param isGenerated - Optional flag indicating if the title was auto-generated (defaults to false, meaning user-set).
   * @returns A promise resolving to the updated session metadata DTO.
   * @throws NotFoundException if the session is not found.
   */
  async updateSessionTitle(
    userId: string,
    sessionId: string,
    title: string,
    isGenerated = false, // Add optional parameter, default to false
  ): Promise<SessionMetadataDto> {
    try {
      // Check if session exists
      const existingSession = await this.sessionRepository.findById(
        userId,
        sessionId,
      );

      if (!existingSession) {
        throw new NotFoundException({
          errors: [
            {
              code: 'SESSION_NOT_FOUND',
              message: 'The requested canvas session was not found.',
              details: { session_id: sessionId },
            },
          ],
        });
      }

      // Update the session title
      // title_generated is true if the title was generated by the backend (isGenerated is true)
      const updatedSession = await this.sessionRepository.update(
        userId,
        sessionId,
        {
          session_title: title,
          title_generated: isGenerated,
        },
      );

      return this.mapToMetadataDto(updatedSession);
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to update title for session ${sessionId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Update a session's language
   * @param userId The ID of the user
   * @param sessionId The ID of the session
   * @param language The new language
   * @returns The updated session metadata
   */
  async updateSessionLanguage(
    userId: string,
    sessionId: string,
    language: SupportedLanguage,
  ): Promise<SessionMetadataDto> {
    try {
      // Check if session exists and if language is locked
      const existingSession = await this.sessionRepository.findById(
        userId,
        sessionId,
      );

      if (!existingSession) {
        throw new NotFoundException({
          errors: [
            {
              code: 'SESSION_NOT_FOUND',
              message: 'The requested canvas session was not found.',
              details: { session_id: sessionId },
            },
          ],
        });
      }

      // Check if language is already locked
      if (existingSession.language_locked) {
        throw new BadRequestException({
          errors: [
            {
              code: 'LANGUAGE_LOCKED',
              message:
                'The language for this session is already locked and cannot be changed.',
              details: { session_id: sessionId },
            },
          ],
        });
      }

      // Update the session language
      const updatedSession = await this.sessionRepository.update(
        userId,
        sessionId,
        {
          language,
        },
      );

      return this.mapToMetadataDto(updatedSession);
    } catch (error: unknown) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      this.logger.error(
        `Failed to update language for session ${sessionId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Updates the inspiration IDs for a specific session.
   * @param userId - The ID of the user owning the session.
   * @param sessionId - The ID of the session to update.
   * @param inspirationIds - The new array of inspiration IDs.
   * @returns A promise resolving when the update is complete.
   * @throws NotFoundException if the session is not found.
   */
  async updateSessionInspirations(
    userId: string,
    sessionId: string,
    inspirationIds: string[],
  ): Promise<void> {
    // Returning void for simplicity, could return updated DTO
    this.logger.debug(`Updating inspiration IDs for session ${sessionId}`);
    try {
      // First, verify the session exists for the user
      const existingSession = await this.sessionRepository.findById(
        userId,
        sessionId,
      );

      if (!existingSession) {
        throw new NotFoundException({
          errors: [
            {
              code: 'SESSION_NOT_FOUND',
              message: 'Session not found during inspiration ID update.',
              details: { session_id: sessionId },
            },
          ],
        });
      }

      // Perform the update using the repository
      await this.sessionRepository.update(userId, sessionId, {
        inspiration_ids: inspirationIds,
      });

      this.logger.log(
        `Successfully updated inspiration IDs for session ${sessionId}`,
      );
    } catch (error: unknown) {
      // Re-throw known exceptions
      if (error instanceof NotFoundException) {
        throw error;
      }
      // Log and throw others
      this.logger.error(
        `Failed to update inspiration IDs for session ${sessionId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new InternalServerErrorException(
        'Could not update session inspiration IDs.',
      );
    }
  }

  /**
   * Delete a session
   * @param userId The ID of the user
   * @param sessionId The ID of the session
   */
  async deleteSession(userId: string, sessionId: string): Promise<void> {
    try {
      // Check if session exists
      const existingSession = await this.sessionRepository.findById(
        userId,
        sessionId,
      );

      if (!existingSession) {
        throw new NotFoundException({
          errors: [
            {
              code: 'SESSION_NOT_FOUND',
              message: 'The requested canvas session was not found.',
              details: { session_id: sessionId },
            },
          ],
        });
      }

      // Delete the session
      await this.sessionRepository.delete(userId, sessionId);

      // TODO: Notify the Backend service to delete associated chat history
      // This would be done in a future implementation

      this.logger.log(`Deleted session ${sessionId} for user ${userId}`);
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to delete session ${sessionId} for user ${userId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Bookmark a session
   * @param userId The ID of the user
   * @param sessionId The ID of the session
   * @returns The updated session metadata
   */
  async bookmarkSession(
    userId: string,
    sessionId: string,
  ): Promise<SessionMetadataDto> {
    try {
      // Check if session exists
      const existingSession = await this.sessionRepository.findById(
        userId,
        sessionId,
      );

      if (!existingSession) {
        throw new NotFoundException({
          errors: [
            {
              code: 'SESSION_NOT_FOUND',
              message: 'The requested canvas session was not found.',
              details: { session_id: sessionId },
            },
          ],
        });
      }

      // Update the session to be bookmarked
      const updatedSession = await this.sessionRepository.update(
        userId,
        sessionId,
        {
          is_bookmarked: true,
        },
      );

      this.logger.log(`Bookmarked session ${sessionId} for user ${userId}`);
      return this.mapToMetadataDto(updatedSession);
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to bookmark session ${sessionId} for user ${userId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Unbookmark a session
   * @param userId The ID of the user
   * @param sessionId The ID of the session
   * @returns The updated session metadata
   */
  async unbookmarkSession(
    userId: string,
    sessionId: string,
  ): Promise<SessionMetadataDto> {
    try {
      // Check if session exists
      const existingSession = await this.sessionRepository.findById(
        userId,
        sessionId,
      );

      if (!existingSession) {
        throw new NotFoundException({
          errors: [
            {
              code: 'SESSION_NOT_FOUND',
              message: 'The requested canvas session was not found.',
              details: { session_id: sessionId },
            },
          ],
        });
      }

      // Update the session to be unbookmarked
      const updatedSession = await this.sessionRepository.update(
        userId,
        sessionId,
        {
          is_bookmarked: false,
        },
      );

      this.logger.log(`Unbookmarked session ${sessionId} for user ${userId}`);
      return this.mapToMetadataDto(updatedSession);
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to unbookmark session ${sessionId} for user ${userId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Map a Session entity to a SessionMetadataDto
   * @param session The session entity
   * @returns The session metadata DTO
   */
  private mapToMetadataDto(session: Session): SessionMetadataDto {
    // Use class-transformer to map entity to DTO
    return plainToInstance(
      SessionMetadataDto,
      {
        session_id: session.session_id,
        session_title: session.session_title,
        language: session.language,
        language_locked: session.language_locked,
        title_generated: session.title_generated,
        created_at: session.created_at,
        last_updated_at: session.last_updated_at,
        is_bookmarked: session.is_bookmarked ?? false,
        inspiration_ids: session.inspiration_ids ?? [],
        timezone: session.timezone ?? undefined,
      },
      { excludeExtraneousValues: true },
    );
  }

  /**
   * Run schema migrations for all sessions
   * @returns Migration statistics
   */
  async migrateAllSessions(): Promise<{
    migrated: number;
    skipped: number;
    failed: number;
  }> {
    try {
      this.logger.log('Starting migration of all sessions');
      return await this.sessionRepository.migrateAllSessions();
    } catch (error: unknown) {
      this.logger.error(
        `Failed to migrate sessions: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Sets the language for a session and locks it.
   * @param userId The ID of the user.
   * @param sessionId The ID of the session.
   * @param language The language to set.
   * @returns The updated session metadata.
   * @throws NotFoundException if the session does not exist.
   */
  async updateSessionMetadata(
    userId: string,
    sessionId: string,
    updates: {
      language?: SupportedLanguage;
      timezone?: string;
    },
  ): Promise<SessionMetadataDto> {
    try {
      // Ensure session exists first
      const existingSession = await this.sessionRepository.findById(
        userId,
        sessionId,
      );
      if (!existingSession) {
        throw new NotFoundException({
          errors: [
            {
              code: 'SESSION_NOT_FOUND',
              message: 'Session not found for language lock update.',
              details: { session_id: sessionId },
            },
          ],
        });
      }

      const updatedSession = await this.sessionRepository.update(
        userId,
        sessionId,
        updates,
      );

      this.logger.log(
        `Updated session metadata for session ${sessionId} for user ${userId} with updates: ${JSON.stringify(updates)}`,
      );
      return this.mapToMetadataDto(updatedSession);
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        // Re-throw NotFoundException directly
        throw error;
      }
      this.logger.error(
        `Failed to update session metadata for session ${sessionId} for user ${userId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      // Throw InternalServerErrorException for other errors
      throw new InternalServerErrorException(
        'Could not update session metadata.',
      );
    }
  }
}
