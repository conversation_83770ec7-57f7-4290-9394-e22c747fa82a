import { Test, TestingModule } from '@nestjs/testing';
import { SessionRepository } from './session.repository';
import { ConfigService } from '@nestjs/config';
import { DynamoMigrationService } from '../database/dynamodb/dynamo-migration.service';
import {
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { UpdateCommandInput, QueryCommandInput } from '@aws-sdk/lib-dynamodb';
import { SupportedLanguage } from 'src/common/languages';

// Define an interface for the expected structure of the PutCommand mock call argument
interface MockPutCommandInput {
  TableName: string;
  Item: {
    PK: string;
    SK: string;
    session_id: string;
    session_title: string;
    session_title_lowercase: string;
    created_at: string;
    last_updated_at: string;
    gsi1_pk: string;
    gsi1_sk: string;
    gsi2_pk: string;
    gsi2_sk: string;
    schema_version: number;
    language: string;
    language_locked: boolean;
    title_generated: boolean;
    // Allow other properties if necessary, matching Session entity
    [key: string]: unknown;
  };
  ConditionExpression: string;
}

interface MockPutCommand {
  input: MockPutCommandInput;
}

interface MockQueryCommand {
  input: QueryCommandInput;
}

// Type for Jest mock functions
type JestMockFunction = jest.Mock<Promise<any>, any[]> & {
  mock: {
    calls: unknown[][];
    results: unknown[];
    instances: unknown[];
    contexts: unknown[];
    lastCall: unknown[];
  };
};

// Mock the DynamoDBDocumentClient with proper typing
const mockDynamoDBDocumentClient = {
  send: jest.fn() as JestMockFunction,
};

describe('SessionRepository', () => {
  let repository: SessionRepository;

  // Create a mock for DynamoMigrationService
  const mockDynamoMigrationService = {
    registerMigration: jest.fn(),
    migrateItem: jest
      .fn()
      .mockImplementation((tableName, item) => Promise.resolve(item)),
    hasMigrationsForTable: jest.fn().mockReturnValue(true),
    saveItem: jest.fn().mockResolvedValue(undefined),
    batchMigrateTable: jest
      .fn()
      .mockResolvedValue({ migrated: 0, skipped: 0, failed: 0 }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SessionRepository,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === 'DYNAMODB_SESSIONS_TABLE') return 'test-table';
              if (key === 'AWS_REGION') return 'us-east-1';
              // Add mock for DYNAMODB_ENDPOINT if needed for specific tests
              return undefined;
            }),
          },
        },
        {
          provide: DynamoMigrationService,
          useValue: mockDynamoMigrationService,
        },
        // Provide the mock client directly - NestJS doesn't instantiate it for us here
        // We'd typically mock the client *used* by the repository constructor
        // For simplicity here, we assume the repo constructor correctly creates the client
      ],
    }).compile();

    repository = module.get<SessionRepository>(SessionRepository);

    // Mock the client instance used within the repository

    (
      repository as unknown as {
        ddbDocClient: typeof mockDynamoDBDocumentClient;
      }
    ).ddbDocClient = mockDynamoDBDocumentClient;
    // Reset mocks before each test
    mockDynamoDBDocumentClient.send.mockClear();
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  // TODO: Add tests for create, findById, findByUser, update, delete
  describe('create', () => {
    it('should call PutCommand with correct parameters', async () => {
      // Arrange
      const sessionData = {
        session_id: 'uuid-123',
        user_id: 'user-abc',
        session_title: 'Test Session',
        language: SupportedLanguage.SIGMA,
        language_locked: false,
        title_generated: false,
        // PK, SK, timestamps, GSI keys etc. are generated internally by create()
      };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({}); // Simulate successful put

      // Act
      await repository.create(sessionData);

      // Assert
      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockPutCommand;
      expect(calledWith.input.TableName).toBe('dev_test-table');
      expect(calledWith.input.Item.PK).toBe('USER#user-abc');
      expect(calledWith.input.Item.SK).toBe('SESSION#uuid-123');
      expect(calledWith.input.Item.session_id).toBe('uuid-123');
      expect(calledWith.input.Item.session_title).toBe('Test Session');
      expect(calledWith.input.Item.schema_version).toBe(4); // Updated to latest schema version
      expect(calledWith.input.ConditionExpression).toBe(
        'attribute_not_exists(PK)',
      );
    });

    // Add tests for ConditionalCheckFailedException and other errors
  });

  describe('findByUser', () => {
    const userId = 'user-123';
    const mockSession = {
      PK: `USER#${userId}`,
      SK: 'SESSION#session-123',
      session_id: 'session-123',
      user_id: userId,
      session_title: 'Test Session',
      is_bookmarked: false,
    };

    beforeEach(() => {
      mockDynamoDBDocumentClient.send.mockClear();
    });

    it('should query sessions and exclude null titles by default using GSI2', async () => {
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [mockSession],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(userId, { limit: 20 });

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      expect(calledWith.input.IndexName).toBe('UserSessionsByTitleSearchGSI');
      expect(calledWith.input.KeyConditionExpression).toBe('gsi2_pk = :pk');
      expect(calledWith.input.ExpressionAttributeValues).toEqual({
        ':pk': `USER#${userId}`,
      });
      expect(calledWith.input.FilterExpression).toBeUndefined();
      expect(result.items).toHaveLength(1);
    });

    it('should filter for bookmarked sessions when bookmarked=true', async () => {
      const bookmarkedSession = { ...mockSession, is_bookmarked: true };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [bookmarkedSession],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(
        userId,
        { limit: 20 },
        undefined,
        true, // bookmarked = true
      );

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      expect(calledWith.input.FilterExpression).toBe(
        'is_bookmarked = :bookmarked',
      );
      expect(calledWith.input.ExpressionAttributeValues).toEqual({
        ':pk': `USER#${userId}`,
        ':bookmarked': true,
      });
      expect(result.items).toHaveLength(1);
    });

    it('should filter for non-bookmarked sessions when bookmarked=false', async () => {
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [mockSession],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(
        userId,
        { limit: 20 },
        undefined,
        false, // bookmarked = false
      );

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      expect(calledWith.input.FilterExpression).toBe(
        '(attribute_not_exists(is_bookmarked) OR is_bookmarked = :bookmarked)',
      );
      expect(calledWith.input.ExpressionAttributeValues).toEqual({
        ':pk': `USER#${userId}`,
        ':bookmarked': false,
      });
      expect(result.items).toHaveLength(1);
    });

    it('should use title search index when titleSearch is provided', async () => {
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [mockSession],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(
        userId,
        { limit: 20 },
        'test', // titleSearch
      );

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      expect(calledWith.input.IndexName).toBe('UserSessionsByTitleSearchGSI');
      expect(calledWith.input.KeyConditionExpression).toBe(
        'gsi2_pk = :pk AND begins_with(gsi2_sk, :titlePrefix)',
      );
      expect(calledWith.input.ExpressionAttributeValues).toEqual({
        ':pk': `USER#${userId}`,
        ':titlePrefix': 'TITLE#test',
      });
      expect(calledWith.input.FilterExpression).toBeUndefined();
      expect(result.items).toHaveLength(1);
    });

    it('should combine title search with bookmark filter', async () => {
      const bookmarkedSession = { ...mockSession, is_bookmarked: true };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [bookmarkedSession],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(
        userId,
        { limit: 20 },
        'test', // titleSearch
        true, // bookmarked = true
      );

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      expect(calledWith.input.IndexName).toBe('UserSessionsByTitleSearchGSI');
      expect(calledWith.input.FilterExpression).toBe(
        'is_bookmarked = :bookmarked',
      );
      expect(calledWith.input.ExpressionAttributeValues).toEqual({
        ':pk': `USER#${userId}`,
        ':titlePrefix': 'TITLE#test',
        ':bookmarked': true,
      });
      expect(result.items).toHaveLength(1);
    });

    it('should exclude null session titles by default using GSI2', async () => {
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [mockSession],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(userId, { limit: 20 });

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      expect(calledWith.input.FilterExpression).toBeUndefined();
      expect(calledWith.input.ExpressionAttributeValues).toEqual({
        ':pk': `USER#${userId}`,
      });
      expect(result.items).toHaveLength(1);
    });

    it('should include null session titles when includeUntitled is true using GSI1', async () => {
      const sessionWithNullTitle = { ...mockSession, session_title: null };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [sessionWithNullTitle],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(
        userId,
        { limit: 20 },
        undefined, // titleSearch
        undefined, // bookmarked
        true, // includeUntitled = true
      );

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      expect(calledWith.input.IndexName).toBe('UserSessionsByLastUpdatedGSI');
      expect(calledWith.input.KeyConditionExpression).toBe('gsi1_pk = :pk');
      expect(calledWith.input.ScanIndexForward).toBe(false);
      expect(calledWith.input.FilterExpression).toBeUndefined();
      expect(result.items).toHaveLength(1);
    });

    it('should exclude null session titles when includeUntitled is false using GSI2', async () => {
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [mockSession],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(
        userId,
        { limit: 20 },
        undefined, // titleSearch
        undefined, // bookmarked
        false, // includeUntitled = false
      );

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      expect(calledWith.input.FilterExpression).toBeUndefined();
      expect(calledWith.input.ExpressionAttributeValues).toEqual({
        ':pk': `USER#${userId}`,
      });
      expect(result.items).toHaveLength(1);
    });

    it('should combine GSI2 filtering with bookmark filtering', async () => {
      const bookmarkedSession = { ...mockSession, is_bookmarked: true };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [bookmarkedSession],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(
        userId,
        { limit: 20 },
        undefined, // titleSearch
        true, // bookmarked = true
        false, // includeUntitled = false
      );

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      expect(calledWith.input.FilterExpression).toBe(
        'is_bookmarked = :bookmarked',
      );
      expect(calledWith.input.ExpressionAttributeValues).toEqual({
        ':pk': `USER#${userId}`,
        ':bookmarked': true,
      });
      expect(result.items).toHaveLength(1);
    });

    it('should include untitled sessions when includeUntitled is true with bookmark filter using GSI1', async () => {
      const bookmarkedSessionWithNullTitle = {
        ...mockSession,
        session_title: null,
        is_bookmarked: true,
      };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [bookmarkedSessionWithNullTitle],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(
        userId,
        { limit: 20 },
        undefined, // titleSearch
        true, // bookmarked = true
        true, // includeUntitled = true
      );

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      expect(calledWith.input.IndexName).toBe('UserSessionsByLastUpdatedGSI');
      expect(calledWith.input.KeyConditionExpression).toBe('gsi1_pk = :pk');
      expect(calledWith.input.ScanIndexForward).toBe(false);
      expect(calledWith.input.FilterExpression).toBe(
        'is_bookmarked = :bookmarked',
      );
      expect(calledWith.input.ExpressionAttributeValues).toEqual({
        ':pk': `USER#${userId}`,
        ':bookmarked': true,
      });
      expect(result.items).toHaveLength(1);
    });

    it('should combine GSI2 filtering with non-bookmarked filter', async () => {
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [mockSession],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(
        userId,
        { limit: 20 },
        undefined, // titleSearch
        false, // bookmarked = false
        false, // includeUntitled = false
      );

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      expect(calledWith.input.FilterExpression).toBe(
        '(attribute_not_exists(is_bookmarked) OR is_bookmarked = :bookmarked)',
      );
      expect(calledWith.input.ExpressionAttributeValues).toEqual({
        ':pk': `USER#${userId}`,
        ':bookmarked': false,
      });
      expect(result.items).toHaveLength(1);
    });

    it('should use GSI2 when includeUntitled is false (automatically excludes null titles)', async () => {
      const userId = 'user-123';
      const mockSessionWithTitle = {
        PK: `USER#${userId}`,
        SK: 'SESSION#session-with-title',
        session_id: 'session-with-title',
        user_id: userId,
        session_title: 'Test Session',
        is_bookmarked: false,
      };

      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [mockSessionWithTitle],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(
        userId,
        { limit: 20 },
        undefined, // titleSearch
        undefined, // bookmarked
        false, // includeUntitled = false (use GSI2)
      );

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      // Should use GSI2 which automatically excludes null titles
      expect(calledWith.input.IndexName).toBe('UserSessionsByTitleSearchGSI');
      expect(calledWith.input.KeyConditionExpression).toBe('gsi2_pk = :pk');
      expect(calledWith.input.FilterExpression).toBeUndefined(); // No filter needed!
      expect(result.items).toHaveLength(1);
    });

    it('should use GSI1 when includeUntitled is true (includes all sessions)', async () => {
      const userId = 'user-123';
      const mockSessionWithNullTitle = {
        PK: `USER#${userId}`,
        SK: 'SESSION#session-null-title',
        session_id: 'session-null-title',
        user_id: userId,
        session_title: null,
        is_bookmarked: false,
      };

      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [mockSessionWithNullTitle],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(
        userId,
        { limit: 20 },
        undefined, // titleSearch
        undefined, // bookmarked
        true, // includeUntitled = true (use GSI1)
      );

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      // Should use GSI1 which includes all sessions
      expect(calledWith.input.IndexName).toBe('UserSessionsByLastUpdatedGSI');
      expect(calledWith.input.KeyConditionExpression).toBe('gsi1_pk = :pk');
      expect(calledWith.input.ScanIndexForward).toBe(false); // Most recent first
      expect(result.items).toHaveLength(1);
    });

    it('should combine GSI2 with title search when titleSearch is provided', async () => {
      const userId = 'user-123';
      const mockSessionWithTitle = {
        PK: `USER#${userId}`,
        SK: 'SESSION#session-with-title',
        session_id: 'session-with-title',
        user_id: userId,
        session_title: 'Test Session',
        is_bookmarked: false,
      };

      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Items: [mockSessionWithTitle],
        LastEvaluatedKey: undefined,
      });

      const result = await repository.findByUser(
        userId,
        { limit: 20 },
        'test', // titleSearch
        undefined, // bookmarked
        false, // includeUntitled = false
      );

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const calledWith = mockDynamoDBDocumentClient.send.mock
        .calls[0][0] as MockQueryCommand;

      // Should use GSI2 with title search
      expect(calledWith.input.IndexName).toBe('UserSessionsByTitleSearchGSI');
      expect(calledWith.input.KeyConditionExpression).toBe(
        'gsi2_pk = :pk AND begins_with(gsi2_sk, :titlePrefix)',
      );
      expect(calledWith.input.ExpressionAttributeValues).toEqual({
        ':pk': `USER#${userId}`,
        ':titlePrefix': 'TITLE#test',
      });
      expect(result.items).toHaveLength(1);
    });
  });

  // --- Tests for update method ---
  describe('update', () => {
    const userId = 'user-update';
    const sessionId = 'session-update';

    // Helper function to get the command input from the mock call
    const getUpdateCommandInput = (
      mockCall: unknown[][],
      callIndex = 0,
    ): UpdateCommandInput | null => {
      if (!mockCall[callIndex]) return null;
      const command = mockCall[callIndex][0];
      // Type guard to check structure
      if (
        !command ||
        typeof command !== 'object' ||
        !('input' in command) ||
        !command.input ||
        typeof command.input !== 'object'
      ) {
        return null;
      }
      // Now safely assert the type
      return (command as { input: UpdateCommandInput }).input;
    };

    beforeEach(() => {
      // Reset send mock before each test in this describe block
      mockDynamoDBDocumentClient.send.mockClear();
      // Default mock for successful update, returning the attributes
      mockDynamoDBDocumentClient.send.mockResolvedValue({ Attributes: {} });
      // Mock findById used internally when update is called with no fields
      // You might need a more specific mock implementation in repository
      jest.spyOn(repository, 'findById').mockResolvedValue(null);
    });

    it('should update title and set title_generated to false if not specified', async () => {
      const updates = { session_title: 'User Title' };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Attributes: { title_generated: false, session_title: 'User Title' },
      }); // Simulate returned value

      await repository.update(userId, sessionId, updates);

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const commandInput = getUpdateCommandInput(
        mockDynamoDBDocumentClient.send.mock.calls,
      );
      expect(commandInput).toBeTruthy();
      expect(commandInput?.ExpressionAttributeValues).toMatchObject({
        ':sessiontitle': 'User Title',
        ':sessiontitlelowercase': 'user title',
        ':titlegenerated': false, // Should be false by default
        ':now': expect.any(String), // Also check for timestamp fields
        ':gsi1skValue': expect.stringContaining('UPDATEDAT#'),
        ':gsi2sk': 'TITLE#user title',
      });
      expect(commandInput?.UpdateExpression).toContain(
        '#titlegenerated = :titlegenerated', // Use sanitized name #titlegenerated
      );
    });

    it('should update title and set title_generated to false when explicitly passed', async () => {
      const updates = {
        session_title: 'Explicit False Title',
        title_generated: false,
      };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Attributes: {
          title_generated: false,
          session_title: 'Explicit False Title',
        },
      });

      await repository.update(userId, sessionId, updates);

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const commandInput = getUpdateCommandInput(
        mockDynamoDBDocumentClient.send.mock.calls,
      );
      expect(commandInput).toBeTruthy();
      expect(commandInput?.ExpressionAttributeValues).toMatchObject({
        ':sessiontitle': 'Explicit False Title',
        ':sessiontitlelowercase': 'explicit false title',
        ':titlegenerated': false,
        ':now': expect.any(String),
        ':gsi1skValue': expect.stringContaining('UPDATEDAT#'),
        ':gsi2sk': 'TITLE#explicit false title',
      });
      expect(commandInput?.UpdateExpression).toContain(
        '#titlegenerated = :titlegenerated',
      );
    });

    it('should update title and set title_generated to true when explicitly passed', async () => {
      const updates = {
        session_title: 'Generated Title',
        title_generated: true,
      };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Attributes: { title_generated: true, session_title: 'Generated Title' },
      });

      await repository.update(userId, sessionId, updates);

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const commandInput = getUpdateCommandInput(
        mockDynamoDBDocumentClient.send.mock.calls,
      );
      expect(commandInput).toBeTruthy();
      expect(commandInput?.ExpressionAttributeValues).toMatchObject({
        ':sessiontitle': 'Generated Title',
        ':sessiontitlelowercase': 'generated title',
        ':titlegenerated': true,
        ':now': expect.any(String),
        ':gsi1skValue': expect.stringContaining('UPDATEDAT#'),
        ':gsi2sk': 'TITLE#generated title',
      });
      expect(commandInput?.UpdateExpression).toContain(
        '#titlegenerated = :titlegenerated',
      );
    });

    it('should update only title_generated to true when passed alone', async () => {
      const updates = { title_generated: true };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Attributes: { title_generated: true },
      });

      await repository.update(userId, sessionId, updates);

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const commandInput = getUpdateCommandInput(
        mockDynamoDBDocumentClient.send.mock.calls,
      );
      expect(commandInput).toBeTruthy();
      expect(commandInput?.ExpressionAttributeValues).toMatchObject({
        ':titlegenerated': true,
        ':now': expect.any(String),
        ':gsi1skValue': expect.stringContaining('UPDATEDAT#'),
      });
      expect(commandInput?.UpdateExpression).toContain(
        '#titlegenerated = :titlegenerated',
      );
    });

    it('should update only title_generated to false when passed alone', async () => {
      const updates = { title_generated: false };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Attributes: { title_generated: false },
      });

      await repository.update(userId, sessionId, updates);

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const commandInput = getUpdateCommandInput(
        mockDynamoDBDocumentClient.send.mock.calls,
      );
      expect(commandInput).toBeTruthy();
      expect(commandInput?.ExpressionAttributeValues).toMatchObject({
        ':titlegenerated': false,
        ':now': expect.any(String),
        ':gsi1skValue': expect.stringContaining('UPDATEDAT#'),
      });
      expect(commandInput?.UpdateExpression).toContain(
        '#titlegenerated = :titlegenerated',
      );
    });

    it('should update language when passed', async () => {
      const updates = { language: SupportedLanguage.KQL };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Attributes: { language: 'kql' },
      });

      await repository.update(userId, sessionId, updates);
      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const commandInput = getUpdateCommandInput(
        mockDynamoDBDocumentClient.send.mock.calls,
      );
      expect(commandInput?.ExpressionAttributeValues).toMatchObject({
        ':language': SupportedLanguage.KQL,
        ':now': expect.any(String),
        ':gsi1skValue': expect.stringContaining('UPDATEDAT#'),
      });
      expect(commandInput?.UpdateExpression).toContain('#language = :language');
    });

    it('should update is_bookmarked to true when passed', async () => {
      const updates = { is_bookmarked: true };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Attributes: { is_bookmarked: true },
      });

      await repository.update(userId, sessionId, updates);

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const commandInput = getUpdateCommandInput(
        mockDynamoDBDocumentClient.send.mock.calls,
      );
      expect(commandInput?.ExpressionAttributeValues).toMatchObject({
        ':isbookmarked': true,
        ':now': expect.any(String),
        ':gsi1skValue': expect.stringContaining('UPDATEDAT#'),
      });
      expect(commandInput?.UpdateExpression).toContain(
        '#isbookmarked = :isbookmarked',
      );
    });

    it('should update is_bookmarked to false when passed', async () => {
      const updates = { is_bookmarked: false };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Attributes: { is_bookmarked: false },
      });

      await repository.update(userId, sessionId, updates);

      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const commandInput = getUpdateCommandInput(
        mockDynamoDBDocumentClient.send.mock.calls,
      );
      expect(commandInput?.ExpressionAttributeValues).toMatchObject({
        ':isbookmarked': false,
        ':now': expect.any(String),
        ':gsi1skValue': expect.stringContaining('UPDATEDAT#'),
      });
      expect(commandInput?.UpdateExpression).toContain(
        '#isbookmarked = :isbookmarked',
      );
    });

    it('should update multiple fields correctly', async () => {
      const updates = {
        session_title: 'Multi Update',
        language: SupportedLanguage.SPL,
        language_locked: true,
        title_generated: false, // Explicitly set false with title
        is_bookmarked: true,
      };
      mockDynamoDBDocumentClient.send.mockResolvedValueOnce({
        Attributes: { ...updates },
      });

      await repository.update(userId, sessionId, updates);
      expect(mockDynamoDBDocumentClient.send).toHaveBeenCalledTimes(1);
      const commandInput = getUpdateCommandInput(
        mockDynamoDBDocumentClient.send.mock.calls,
      );
      expect(commandInput?.ExpressionAttributeValues).toMatchObject({
        ':sessiontitle': 'Multi Update',
        ':sessiontitlelowercase': 'multi update',
        ':titlegenerated': false,
        ':language': SupportedLanguage.SPL,
        ':languagelocked': true,
        ':isbookmarked': true,
        ':now': expect.any(String),
        ':gsi1skValue': expect.stringContaining('UPDATEDAT#'),
        ':gsi2sk': 'TITLE#multi update',
      });
      expect(commandInput?.UpdateExpression).toContain(
        '#sessiontitle = :sessiontitle',
      );
      expect(commandInput?.UpdateExpression).toContain(
        '#titlegenerated = :titlegenerated',
      );
      expect(commandInput?.UpdateExpression).toContain('#language = :language');
      expect(commandInput?.UpdateExpression).toContain(
        '#languagelocked = :languagelocked',
      );
      expect(commandInput?.UpdateExpression).toContain(
        '#isbookmarked = :isbookmarked',
      );
    });

    // Add tests for ConditionalCheckFailedException (session not found) and other errors
    it('should throw NotFoundException if ConditionalCheckFailedException occurs', async () => {
      const updates = { session_title: 'Not Found Update' };
      // Simulate ConditionalCheckFailedException
      const error = new Error('ConditionalCheckFailedException');
      error.name = 'ConditionalCheckFailedException';
      mockDynamoDBDocumentClient.send.mockRejectedValueOnce(error);

      await expect(
        repository.update(userId, sessionId, updates),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw InternalServerErrorException for other DDB errors', async () => {
      const updates = { session_title: 'Error Update' };
      // Simulate a generic DDB error
      mockDynamoDBDocumentClient.send.mockRejectedValueOnce(
        new Error('DDB Error'),
      );

      await expect(
        repository.update(userId, sessionId, updates),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });
  // --- End Tests for update method ---
});
