import { Expose } from 'class-transformer';
import { SupportedLanguage } from 'src/common/languages';

export class Session {
  @Expose()
  PK: string; // USER#{user_id}

  @Expose()
  SK: string; // SESSION#{session_id}

  @Expose()
  session_id: string;

  @Expose()
  user_id: string;

  @Expose()
  session_title: string | null;

  @Expose()
  session_title_lowercase: string | null;

  @Expose()
  language: SupportedLanguage;

  @Expose()
  language_locked: boolean;

  @Expose()
  title_generated: boolean;

  @Expose()
  created_at: string; // ISO 8601

  @Expose()
  last_updated_at: string; // ISO 8601

  @Expose()
  is_bookmarked?: boolean; // Optional bookmark status

  @Expose()
  timezone?: string; // Optional timezone

  // GSI Keys
  @Expose()
  gsi1_pk: string; // USER#{user_id}

  @Expose()
  gsi1_sk: string; // UPDATEDAT#{last_updated_at}

  @Expose()
  gsi2_pk: string; // USER#{user_id}

  @Expose()
  gsi2_sk: string; // TITLE#{session_title_lowercase}

  // Optional version attribute for future schema migrations
  @Expose()
  schema_version?: number;

  // Add optional array for inspiration IDs
  @Expose()
  inspiration_ids?: string[];
}
