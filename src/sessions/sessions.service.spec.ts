import { Test, TestingModule } from '@nestjs/testing';
import { SessionsService } from './sessions.service';
import { SessionRepository } from './session.repository';
import {
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Session } from './entities/session.entity';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { SupportedLanguage } from 'src/common/languages';

// Mock the UUID generation to return a consistent value
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mocked-uuid'),
}));

describe('SessionsService', () => {
  let service: SessionsService;

  // Mock data
  const mockUserId = 'user123';
  const mockSessionId = 'session123';
  const mockInspirationIds = ['insp1', 'insp2'];
  const mockSession: Session = {
    PK: `USER#${mockUserId}`,
    SK: `SESSION#${mockSessionId}`,
    session_id: mockSessionId,
    user_id: mockUserId,
    session_title: 'Test Session',
    session_title_lowercase: 'test session',
    language: SupportedLanguage.SIGMA,
    language_locked: false,
    title_generated: false,
    created_at: '2023-10-27T10:00:00Z',
    last_updated_at: '2023-10-27T10:00:00Z',
    is_bookmarked: false,
    gsi1_pk: `USER#${mockUserId}`,
    gsi1_sk: `UPDATEDAT#2023-10-27T10:00:00Z`,
    gsi2_pk: `USER#${mockUserId}`,
    gsi2_sk: `TITLE#test session`,
    schema_version: 1,
    inspiration_ids: [], // Add field to mock data, default empty
  };

  // Mock repository methods
  const mockRepositoryMethods = {
    create: jest.fn(),
    findById: jest.fn(),
    findByUser: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    migrateAllSessions: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      imports: [EventEmitterModule.forRoot()],
      providers: [
        SessionsService,
        {
          provide: SessionRepository,
          useValue: mockRepositoryMethods,
        },
      ],
    }).compile();

    service = module.get<SessionsService>(SessionsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSession', () => {
    it('should create a session with default values', async () => {
      mockRepositoryMethods.create.mockResolvedValue(mockSession);

      const result = await service.createSession(mockUserId);

      expect(mockRepositoryMethods.create).toHaveBeenCalledWith({
        session_id: 'mocked-uuid',
        user_id: mockUserId,
        session_title: null,
        language: SupportedLanguage.SIGMA,
        language_locked: false,
        title_generated: false,
      });

      expect(result).toEqual({
        session_id: mockSessionId,
        session_title: 'Test Session',
        language_locked: false,
        language: SupportedLanguage.SIGMA,
        title_generated: false,
        created_at: '2023-10-27T10:00:00Z',
        last_updated_at: '2023-10-27T10:00:00Z',
        is_bookmarked: false,
        inspiration_ids: [],
      });
    });

    it('should propagate errors', async () => {
      const error = new Error('Repository error');
      mockRepositoryMethods.create.mockRejectedValue(error);

      await expect(service.createSession(mockUserId)).rejects.toThrow(error);
    });
  });

  describe('getSessions', () => {
    it('should return paginated sessions', async () => {
      mockRepositoryMethods.findByUser.mockResolvedValue({
        items: [mockSession],
        lastEvaluatedKey: undefined,
      });

      const result = await service.getSessions(
        mockUserId,
        1,
        20,
        undefined,
        undefined,
      );

      expect(mockRepositoryMethods.findByUser).toHaveBeenCalledWith(
        mockUserId,
        { limit: 20 },
        undefined,
        undefined,
        undefined,
      );

      expect(result.data).toHaveLength(1);
      expect(result.meta.page).toBe(1);
      expect(result.meta.page_size).toBe(20);
      expect(result.meta.total_items).toBe(1);
      expect(result.meta.total_pages).toBe(1);
    });

    it('should handle pagination beyond page 1', async () => {
      mockRepositoryMethods.findByUser
        .mockResolvedValueOnce({
          items: [mockSession],
          lastEvaluatedKey: { some: 'key' },
        })
        .mockResolvedValueOnce({
          items: [{ ...mockSession, session_id: 'session456' }],
          lastEvaluatedKey: undefined,
        });

      const result = await service.getSessions(
        mockUserId,
        2,
        1,
        undefined,
        undefined,
      );

      // First call to get the lastEvaluatedKey for page 1
      expect(mockRepositoryMethods.findByUser).toHaveBeenNthCalledWith(
        1,
        mockUserId,
        { limit: 1 },
        undefined,
        undefined,
        undefined,
      );

      // Second call to get the items for page 2
      expect(mockRepositoryMethods.findByUser).toHaveBeenNthCalledWith(
        2,
        mockUserId,
        { limit: 1, lastEvaluatedKey: { some: 'key' } },
        undefined,
        undefined,
        undefined,
      );

      expect(result.data).toHaveLength(1);
      expect(result.meta.page).toBe(2);
    });

    it('should pass bookmarked filter to repository', async () => {
      mockRepositoryMethods.findByUser.mockResolvedValue({
        items: [mockSession],
        lastEvaluatedKey: undefined,
      });

      await service.getSessions(mockUserId, 1, 20, undefined, true);

      expect(mockRepositoryMethods.findByUser).toHaveBeenCalledWith(
        mockUserId,
        { limit: 20 },
        undefined,
        true,
        undefined,
      );
    });

    it('should pass title search and bookmarked filter to repository', async () => {
      mockRepositoryMethods.findByUser.mockResolvedValue({
        items: [mockSession],
        lastEvaluatedKey: undefined,
      });

      await service.getSessions(mockUserId, 1, 20, 'test', false);

      expect(mockRepositoryMethods.findByUser).toHaveBeenCalledWith(
        mockUserId,
        { limit: 20 },
        'test',
        false,
        undefined,
      );
    });
  });

  describe('getSessionById', () => {
    it('should return a session by ID', async () => {
      mockRepositoryMethods.findById.mockResolvedValue(mockSession);

      const result = await service.getSessionById(mockUserId, mockSessionId);

      expect(mockRepositoryMethods.findById).toHaveBeenCalledWith(
        mockUserId,
        mockSessionId,
      );

      expect(result).toEqual({
        session_id: mockSessionId,
        session_title: 'Test Session',
        language: SupportedLanguage.SIGMA,
        language_locked: false,
        title_generated: false,
        created_at: '2023-10-27T10:00:00Z',
        last_updated_at: '2023-10-27T10:00:00Z',
        is_bookmarked: false,
        inspiration_ids: [],
      });
    });

    it('should throw NotFoundException if session does not exist', async () => {
      mockRepositoryMethods.findById.mockResolvedValue(null);

      await expect(
        service.getSessionById(mockUserId, mockSessionId),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateSessionTitle', () => {
    it('should update a session title', async () => {
      mockRepositoryMethods.findById.mockResolvedValue(mockSession);
      mockRepositoryMethods.update.mockResolvedValue({
        ...mockSession,
        session_title: 'Updated Title',
        title_generated: true,
      });

      const result = await service.updateSessionTitle(
        mockUserId,
        mockSessionId,
        'Updated Title',
      );

      expect(mockRepositoryMethods.update).toHaveBeenCalledWith(
        mockUserId,
        mockSessionId,
        {
          session_title: 'Updated Title',
          title_generated: false,
        },
      );

      expect(result.session_title).toBe('Updated Title');
      expect(result.title_generated).toBe(true);
    });

    it('should throw NotFoundException if session does not exist', async () => {
      mockRepositoryMethods.findById.mockResolvedValue(null);

      await expect(
        service.updateSessionTitle(mockUserId, mockSessionId, 'New Title'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateSessionLanguage', () => {
    const userId = 'user-123';
    const sessionId = 'session-abc';
    const newLanguage = SupportedLanguage.KQL;

    it('should successfully update the language for an unlocked session', async () => {
      const existingSession: Session = {
        ...mockSession,
        session_id: sessionId,
        user_id: userId,
        language: SupportedLanguage.SIGMA,
        language_locked: false, // Unlocked
      };
      const updatedSession: Session = {
        ...existingSession,
        language: newLanguage,
        language_locked: false, // Remains unlocked
        last_updated_at: new Date().toISOString(),
      };

      mockRepositoryMethods.findById.mockResolvedValue(existingSession);
      mockRepositoryMethods.update.mockResolvedValue(updatedSession);

      const result = await service.updateSessionLanguage(
        userId,
        sessionId,
        newLanguage,
      );

      expect(mockRepositoryMethods.findById).toHaveBeenCalledWith(
        userId,
        sessionId,
      );
      expect(mockRepositoryMethods.update).toHaveBeenCalledWith(
        userId,
        sessionId,
        { language: newLanguage },
      );
      expect(result).toBeDefined();
      expect(result.language).toBe(newLanguage);
      expect(result.language_locked).toBe(false);
    });

    it('should throw BadRequestException if the session language is locked', async () => {
      const existingSession: Session = {
        ...mockSession,
        session_id: sessionId,
        user_id: userId,
        language: SupportedLanguage.SIGMA,
        language_locked: true, // Locked
      };

      mockRepositoryMethods.findById.mockResolvedValue(existingSession);

      await expect(
        service.updateSessionLanguage(userId, sessionId, newLanguage),
      ).rejects.toThrow(BadRequestException);
      expect(mockRepositoryMethods.update).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException if the session is not found', async () => {
      mockRepositoryMethods.findById.mockResolvedValue(null);

      await expect(
        service.updateSessionLanguage(userId, sessionId, newLanguage),
      ).rejects.toThrow(NotFoundException);
      expect(mockRepositoryMethods.update).not.toHaveBeenCalled();
    });

    it('should re-throw InternalServerErrorException from repository update', async () => {
      const existingSession: Session = {
        ...mockSession,
        session_id: sessionId,
        user_id: userId,
        language_locked: false,
      };
      mockRepositoryMethods.findById.mockResolvedValue(existingSession);
      mockRepositoryMethods.update.mockRejectedValue(
        new InternalServerErrorException('DB error'),
      );

      await expect(
        service.updateSessionLanguage(userId, sessionId, newLanguage),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('updateSessionInspirations', () => {
    it('should call repository update with correct inspiration IDs', async () => {
      mockRepositoryMethods.findById.mockResolvedValue(mockSession);
      // Mock the update method to resolve successfully (return value isn't checked here)
      mockRepositoryMethods.update.mockResolvedValue(undefined);

      await service.updateSessionInspirations(
        mockUserId,
        mockSessionId,
        mockInspirationIds,
      );

      // Verify findById was called
      expect(mockRepositoryMethods.findById).toHaveBeenCalledWith(
        mockUserId,
        mockSessionId,
      );

      // Verify update was called with the correct inspiration_ids payload
      expect(mockRepositoryMethods.update).toHaveBeenCalledWith(
        mockUserId,
        mockSessionId,
        { inspiration_ids: mockInspirationIds },
      );
    });

    it('should throw NotFoundException if session does not exist', async () => {
      mockRepositoryMethods.findById.mockResolvedValue(null); // Simulate session not found

      await expect(
        service.updateSessionInspirations(
          mockUserId,
          mockSessionId,
          mockInspirationIds,
        ),
      ).rejects.toThrow(NotFoundException);

      // Ensure update was not called if findById failed
      expect(mockRepositoryMethods.update).not.toHaveBeenCalled();
    });

    it('should throw InternalServerErrorException on repository update error', async () => {
      const repositoryError = new Error('DynamoDB update failed');
      mockRepositoryMethods.findById.mockResolvedValue(mockSession); // Session exists
      mockRepositoryMethods.update.mockRejectedValue(repositoryError); // Simulate update error

      await expect(
        service.updateSessionInspirations(
          mockUserId,
          mockSessionId,
          mockInspirationIds,
        ),
      ).rejects.toThrow(InternalServerErrorException);

      // Verify update was attempted
      expect(mockRepositoryMethods.update).toHaveBeenCalledWith(
        mockUserId,
        mockSessionId,
        { inspiration_ids: mockInspirationIds },
      );
    });
  });

  describe('deleteSession', () => {
    it('should delete a session', async () => {
      mockRepositoryMethods.findById.mockResolvedValue(mockSession);
      mockRepositoryMethods.delete.mockResolvedValue(undefined);

      await service.deleteSession(mockUserId, mockSessionId);

      expect(mockRepositoryMethods.delete).toHaveBeenCalledWith(
        mockUserId,
        mockSessionId,
      );
    });

    it('should throw NotFoundException if session does not exist', async () => {
      mockRepositoryMethods.findById.mockResolvedValue(null);

      await expect(
        service.deleteSession(mockUserId, mockSessionId),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('bookmarkSession', () => {
    it('should bookmark a session successfully', async () => {
      const updatedSession = { ...mockSession, is_bookmarked: true };
      mockRepositoryMethods.findById.mockResolvedValue(mockSession);
      mockRepositoryMethods.update.mockResolvedValue(updatedSession);

      const result = await service.bookmarkSession(mockUserId, mockSessionId);

      expect(mockRepositoryMethods.findById).toHaveBeenCalledWith(
        mockUserId,
        mockSessionId,
      );
      expect(mockRepositoryMethods.update).toHaveBeenCalledWith(
        mockUserId,
        mockSessionId,
        { is_bookmarked: true },
      );
      expect(result.is_bookmarked).toBe(true);
    });

    it('should throw NotFoundException if session does not exist', async () => {
      mockRepositoryMethods.findById.mockResolvedValue(null);

      await expect(
        service.bookmarkSession(mockUserId, mockSessionId),
      ).rejects.toThrow(NotFoundException);

      expect(mockRepositoryMethods.update).not.toHaveBeenCalled();
    });

    it('should propagate other errors from repository', async () => {
      const repositoryError = new InternalServerErrorException('DB error');
      mockRepositoryMethods.findById.mockResolvedValue(mockSession);
      mockRepositoryMethods.update.mockRejectedValue(repositoryError);

      await expect(
        service.bookmarkSession(mockUserId, mockSessionId),
      ).rejects.toThrow(repositoryError);
    });
  });

  describe('unbookmarkSession', () => {
    it('should unbookmark a session successfully', async () => {
      const bookmarkedSession = { ...mockSession, is_bookmarked: true };
      const updatedSession = { ...mockSession, is_bookmarked: false };
      mockRepositoryMethods.findById.mockResolvedValue(bookmarkedSession);
      mockRepositoryMethods.update.mockResolvedValue(updatedSession);

      const result = await service.unbookmarkSession(mockUserId, mockSessionId);

      expect(mockRepositoryMethods.findById).toHaveBeenCalledWith(
        mockUserId,
        mockSessionId,
      );
      expect(mockRepositoryMethods.update).toHaveBeenCalledWith(
        mockUserId,
        mockSessionId,
        { is_bookmarked: false },
      );
      expect(result.is_bookmarked).toBe(false);
    });

    it('should throw NotFoundException if session does not exist', async () => {
      mockRepositoryMethods.findById.mockResolvedValue(null);

      await expect(
        service.unbookmarkSession(mockUserId, mockSessionId),
      ).rejects.toThrow(NotFoundException);

      expect(mockRepositoryMethods.update).not.toHaveBeenCalled();
    });

    it('should propagate other errors from repository', async () => {
      const repositoryError = new InternalServerErrorException('DB error');
      mockRepositoryMethods.findById.mockResolvedValue(mockSession);
      mockRepositoryMethods.update.mockRejectedValue(repositoryError);

      await expect(
        service.unbookmarkSession(mockUserId, mockSessionId),
      ).rejects.toThrow(repositoryError);
    });
  });

  describe('updateSessionMetadata', () => {
    const userId = 'user-789';
    const sessionId = 'session-def';
    const newLanguage = SupportedLanguage.SPL;
    const newTimezone = 'America/New_York';

    it('should successfully update language only', async () => {
      const existingSession: Session = {
        ...mockSession,
        session_id: sessionId,
        user_id: userId,
        language: SupportedLanguage.SIGMA,
        timezone: undefined,
      };
      const updatedSession: Session = {
        ...existingSession,
        language: newLanguage,
        last_updated_at: new Date().toISOString(),
      };

      mockRepositoryMethods.findById.mockResolvedValue(existingSession);
      mockRepositoryMethods.update.mockResolvedValue(updatedSession);

      const result = await service.updateSessionMetadata(userId, sessionId, {
        language: newLanguage,
      });

      expect(mockRepositoryMethods.findById).toHaveBeenCalledWith(
        userId,
        sessionId,
      );
      expect(mockRepositoryMethods.update).toHaveBeenCalledWith(
        userId,
        sessionId,
        { language: newLanguage },
      );
      expect(result).toBeDefined();
      expect(result.language).toBe(newLanguage);
    });

    it('should successfully update timezone only', async () => {
      const existingSession: Session = {
        ...mockSession,
        session_id: sessionId,
        user_id: userId,
        language: SupportedLanguage.SIGMA,
        timezone: undefined,
      };
      const updatedSession: Session = {
        ...existingSession,
        timezone: newTimezone,
        last_updated_at: new Date().toISOString(),
      };

      mockRepositoryMethods.findById.mockResolvedValue(existingSession);
      mockRepositoryMethods.update.mockResolvedValue(updatedSession);

      const result = await service.updateSessionMetadata(userId, sessionId, {
        timezone: newTimezone,
      });

      expect(mockRepositoryMethods.findById).toHaveBeenCalledWith(
        userId,
        sessionId,
      );
      expect(mockRepositoryMethods.update).toHaveBeenCalledWith(
        userId,
        sessionId,
        { timezone: newTimezone },
      );
      expect(result).toBeDefined();
      expect(result.timezone).toBe(newTimezone);
    });

    it('should successfully update both language and timezone', async () => {
      const existingSession: Session = {
        ...mockSession,
        session_id: sessionId,
        user_id: userId,
        language: SupportedLanguage.SIGMA,
        timezone: undefined,
      };
      const updatedSession: Session = {
        ...existingSession,
        language: newLanguage,
        timezone: newTimezone,
        last_updated_at: new Date().toISOString(),
      };

      mockRepositoryMethods.findById.mockResolvedValue(existingSession);
      mockRepositoryMethods.update.mockResolvedValue(updatedSession);

      const result = await service.updateSessionMetadata(userId, sessionId, {
        language: newLanguage,
        timezone: newTimezone,
      });

      expect(mockRepositoryMethods.findById).toHaveBeenCalledWith(
        userId,
        sessionId,
      );
      expect(mockRepositoryMethods.update).toHaveBeenCalledWith(
        userId,
        sessionId,
        { language: newLanguage, timezone: newTimezone },
      );
      expect(result).toBeDefined();
      expect(result.language).toBe(newLanguage);
      expect(result.timezone).toBe(newTimezone);
    });

    it('should throw NotFoundException if session does not exist', async () => {
      mockRepositoryMethods.findById.mockResolvedValue(null);

      await expect(
        service.updateSessionMetadata(userId, sessionId, {
          language: newLanguage,
        }),
      ).rejects.toThrow(NotFoundException);

      expect(mockRepositoryMethods.update).not.toHaveBeenCalled();
    });

    it('should throw InternalServerErrorException if repository update fails', async () => {
      const existingSession: Session = {
        ...mockSession,
        session_id: sessionId,
        user_id: userId,
      };
      mockRepositoryMethods.findById.mockResolvedValue(existingSession);
      mockRepositoryMethods.update.mockRejectedValue(
        new InternalServerErrorException('DB update failed'),
      );

      await expect(
        service.updateSessionMetadata(userId, sessionId, {
          language: newLanguage,
        }),
      ).rejects.toThrow(InternalServerErrorException);

      expect(mockRepositoryMethods.findById).toHaveBeenCalledWith(
        userId,
        sessionId,
      );
      expect(mockRepositoryMethods.update).toHaveBeenCalledWith(
        userId,
        sessionId,
        { language: newLanguage },
      );
    });

    it('should throw InternalServerErrorException if findById fails unexpectedly', async () => {
      mockRepositoryMethods.findById.mockRejectedValue(
        new Error('Unexpected find error'),
      );

      await expect(
        service.updateSessionMetadata(userId, sessionId, {
          timezone: newTimezone,
        }),
      ).rejects.toThrow(InternalServerErrorException);

      expect(mockRepositoryMethods.findById).toHaveBeenCalledWith(
        userId,
        sessionId,
      );
      expect(mockRepositoryMethods.update).not.toHaveBeenCalled();
    });
  });
});
