import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  Query<PERSON>ommand,
  UpdateCommand,
  DeleteCommand,
  QueryCommandInput,
  UpdateCommandInput,
} from '@aws-sdk/lib-dynamodb';
import { Session } from './entities/session.entity';
import { ConditionalCheckFailedException } from '@aws-sdk/client-dynamodb';
import { DynamoMigrationService } from '../database/dynamodb/dynamo-migration.service';
import { SupportedLanguage } from 'src/common/languages';
// UUID import removed as it's not used
// import { v4 as uuidv4 } from 'uuid';

/**
 * Represents metadata for a detection canvas session
 */
export interface SessionMetadata {
  session_id: string;
  session_title: string;
  language: SupportedLanguage | null;
  language_locked: boolean;
  title_generated: boolean;
  created_at: string;
  last_updated_at: string;
}

// Define the input type required by the create method
// These are the fields the caller must provide
type CreateSessionInput = Pick<
  Session,
  | 'session_id'
  | 'user_id'
  | 'session_title'
  | 'language'
  | 'language_locked'
  | 'title_generated'
>;

@Injectable()
export class SessionRepository {
  private readonly logger = new Logger(SessionRepository.name);
  private readonly tableName: string;
  private readonly ddbDocClient: DynamoDBDocumentClient;

  constructor(
    private configService: ConfigService,
    private migrationService: DynamoMigrationService,
  ) {
    const baseTableName = this.configService.get<string>(
      'DYNAMODB_SESSIONS_TABLE',
      {
        infer: true,
      },
    )!;

    if (!baseTableName) {
      throw new InternalServerErrorException(
        'DYNAMODB_SESSIONS_TABLE is not configured.',
      );
    }

    // Add environment prefix to table name
    const env = this.configService.get<string>('ENV') || 'dev';
    this.tableName = `${env}_${baseTableName}`;

    const endpoint = this.configService.get<string>('DYNAMODB_ENDPOINT');
    const region = this.configService.get<string>('AWS_REGION', {
      infer: true,
    });

    if (!region) {
      this.logger.error('AWS_REGION environment variable is not set.');
      throw new InternalServerErrorException('AWS_REGION is not configured.');
    }

    const ddbClient = new DynamoDBClient({
      region: region,
      endpoint: endpoint,
    });
    this.ddbDocClient = DynamoDBDocumentClient.from(ddbClient);

    this.logger.log(
      `SessionRepository initialized for table: ${this.tableName}${endpoint ? ' (Endpoint: ' + endpoint + ')' : ''}`,
    );

    // Register migrations
    this.registerMigrations();
  }

  /**
   * Register schema migrations for sessions table
   */
  private registerMigrations(): void {
    // Example migration to add a new field
    this.migrationService.registerMigration(this.tableName, {
      version: 2,
      description: 'Add additional_data field',
      up: (item) => ({
        ...item,
        additional_data:
          (item.additional_data as Record<string, unknown>) || {},
      }),
    });

    // Example migration for a more complex schema change
    this.migrationService.registerMigration(this.tableName, {
      version: 3,
      description: 'Restructure user preferences',
      up: (item) => {
        // This is just an example - implement actual migrations based on your schema needs
        if (item.preferences) {
          return {
            ...item,
            preferences: {
              ...(item.preferences as Record<string, unknown>),
              theme:
                ((item.preferences as Record<string, unknown>)
                  .theme as string) || 'light',
              notifications:
                ((item.preferences as Record<string, unknown>)
                  .notifications as boolean) || true,
            },
          };
        }
        return {
          ...item,
          preferences: {
            theme: 'light',
            notifications: true,
          },
        };
      },
    });
  }

  /**
   * Creates a new session item in DynamoDB.
   */
  async create(sessionData: CreateSessionInput): Promise<Session> {
    const pk = `USER#${sessionData.user_id}`;
    const sk = `SESSION#${sessionData.session_id}`;
    const timestamp = new Date().toISOString();
    const gsi1_pk = pk;
    const gsi1_sk = `UPDATEDAT#${timestamp}`;
    const gsi2_pk = pk;
    const session_title_lowercase = sessionData.session_title?.toLowerCase();
    const gsi2_sk = `TITLE#${session_title_lowercase}`;

    const fullSession: Session = {
      ...sessionData,
      PK: pk,
      SK: sk,
      session_title_lowercase: session_title_lowercase ?? null,
      created_at: timestamp,
      last_updated_at: timestamp,
      gsi1_pk,
      gsi1_sk,
      gsi2_pk,
      gsi2_sk,
      schema_version: 4, // Use the latest schema version for new items
    };

    const command = new PutCommand({
      TableName: this.tableName,
      Item: fullSession,
      ConditionExpression: 'attribute_not_exists(PK)', // Prevent overwriting
    });

    try {
      await this.ddbDocClient.send(command);
      this.logger.log(`Created session: ${fullSession.session_id}`);
      return fullSession;
    } catch (error) {
      if (error instanceof ConditionalCheckFailedException) {
        this.logger.warn(
          `Attempted to create session that already exists: ${fullSession.session_id}`,
        );
        throw new Error('Session already exists.');
      } else {
        this.logger.error(
          `Failed to create session ${fullSession.session_id}: ${error}`,
        );
        throw new InternalServerErrorException('Could not create session.');
      }
    }
  }

  /**
   * Finds a session by its user ID and session ID.
   */
  async findById(userId: string, sessionId: string): Promise<Session | null> {
    const command = new GetCommand({
      TableName: this.tableName,
      Key: {
        PK: `USER#${userId}`,
        SK: `SESSION#${sessionId}`,
      },
    });

    try {
      const result = await this.ddbDocClient.send(command);
      if (!result.Item) {
        this.logger.log(
          `Session not found for user ${userId}, session ${sessionId}`,
        );
        return null;
      }

      // Apply migrations if necessary
      const migratedItem = await this.migrationService.migrateItem(
        this.tableName,
        result.Item,
      );

      return migratedItem as Session;
    } catch (error) {
      this.logger.error(
        `Failed to find session ${sessionId} for user ${userId}: ${error}`,
      );
      throw new InternalServerErrorException('Could not retrieve session.');
    }
  }

  /**
   * Finds sessions for a user, optionally filtering by title prefix.
   * Supports pagination using lastEvaluatedKey.
   */
  async findByUser(
    userId: string,
    paginationOptions: {
      limit: number;
      lastEvaluatedKey?: Record<string, unknown>;
    },
    titleSearch?: string,
    bookmarked?: boolean,
    includeUntitled?: boolean,
  ): Promise<{ items: Session[]; lastEvaluatedKey?: Record<string, unknown> }> {
    const pkValue = `USER#${userId}`;
    let queryInput: QueryCommandInput = {
      TableName: this.tableName,
      Limit: paginationOptions.limit,
      ExclusiveStartKey: paginationOptions.lastEvaluatedKey,
    };

    // Choose the appropriate GSI based on filtering needs
    if (includeUntitled === true) {
      // Use GSI1 (includes all sessions) when we want untitled sessions
      queryInput = {
        ...queryInput,
        IndexName: 'UserSessionsByLastUpdatedGSI',
        KeyConditionExpression: 'gsi1_pk = :pk',
        ExpressionAttributeValues: {
          ':pk': pkValue,
        },
        ScanIndexForward: false, // Most recent first
      };
    } else {
      // Use GSI2 (automatically excludes null titles via sparse index) when we don't want untitled sessions
      queryInput = {
        ...queryInput,
        IndexName: 'UserSessionsByTitleSearchGSI',
        KeyConditionExpression: 'gsi2_pk = :pk',
        ExpressionAttributeValues: {
          ':pk': pkValue,
        },
      };

      // Add title search if provided
      if (titleSearch) {
        queryInput.KeyConditionExpression +=
          ' AND begins_with(gsi2_sk, :titlePrefix)';
        queryInput.ExpressionAttributeValues = {
          ...queryInput.ExpressionAttributeValues,
          ':titlePrefix': `TITLE#${titleSearch.toLowerCase()}`,
        };
      }
    }

    // Add bookmark filter if specified
    const filterConditions: string[] = [];
    if (bookmarked !== undefined) {
      if (bookmarked) {
        // Filter for bookmarked sessions
        filterConditions.push('is_bookmarked = :bookmarked');
        queryInput.ExpressionAttributeValues = {
          ...queryInput.ExpressionAttributeValues,
          ':bookmarked': true,
        };
      } else {
        // Filter for non-bookmarked sessions (either false or doesn't exist)
        filterConditions.push(
          '(attribute_not_exists(is_bookmarked) OR is_bookmarked = :bookmarked)',
        );
        queryInput.ExpressionAttributeValues = {
          ...queryInput.ExpressionAttributeValues,
          ':bookmarked': false,
        };
      }
    }

    // Apply filter conditions if any
    if (filterConditions.length > 0) {
      queryInput.FilterExpression = filterConditions.join(' AND ');
    }

    const command = new QueryCommand(queryInput);

    try {
      const result = await this.ddbDocClient.send(command);

      // Apply migrations to each item in the result
      const migratedItems = await Promise.all(
        (result.Items || []).map(async (item) => {
          const migratedItem = await this.migrationService.migrateItem(
            this.tableName,
            item,
          );
          return migratedItem as Session;
        }),
      );

      return {
        items: migratedItems,
        lastEvaluatedKey: result.LastEvaluatedKey
          ? { ...result.LastEvaluatedKey }
          : undefined,
      };
    } catch (error) {
      this.logger.error(`Failed to find sessions for user ${userId}: ${error}`);
      throw new InternalServerErrorException('Could not retrieve sessions.');
    }
  }

  /**
   * Run schema migrations for all sessions
   */
  async migrateAllSessions(): Promise<{
    migrated: number;
    skipped: number;
    failed: number;
  }> {
    try {
      this.logger.log('Starting migration of all sessions');

      // Check if there are any migrations registered
      if (!this.migrationService.hasMigrationsForTable(this.tableName)) {
        this.logger.log('No migrations registered for sessions table');
        return { migrated: 0, skipped: 0, failed: 0 };
      }

      try {
        const result = await this.migrationService.batchMigrateTable(
          this.tableName,
          ['PK', 'SK'],
        );

        this.logger.log(`Migration completed: ${JSON.stringify(result)}`);
        return result;
      } catch (migrationError) {
        if (
          migrationError instanceof Error &&
          (migrationError.message.includes('No migrations') ||
            migrationError.message.includes('No items'))
        ) {
          const message = migrationError.message || 'No migrations needed';
          this.logger.log(`Migration note: ${message}`);
          return { migrated: 0, skipped: 0, failed: 0 };
        }

        this.logger.warn(`Note during migration: ${String(migrationError)}`);
        return { migrated: 0, skipped: 0, failed: 0 };
      }
    } catch (error) {
      // This is the outer catch for truly critical errors
      this.logger.error(`Critical error during migration: ${String(error)}`);
      return { migrated: 0, skipped: 0, failed: 0 };
    }
  }

  /**
   * Updates specific attributes of a session.
   * Automatically updates last_updated_at and corresponding GSI keys.
   */
  async update(
    userId: string,
    sessionId: string,
    updates: Partial<
      Omit<Session, 'PK' | 'SK' | 'user_id' | 'session_id' | 'created_at'> & {
        title_generated?: boolean;
        inspiration_ids?: string[];
      }
    >,
  ): Promise<Session> {
    const timestamp = new Date().toISOString();

    // Initialize update builder
    const updateBuilder = this.createUpdateBuilder(userId, timestamp);

    // Add fields to update
    if (updates.session_title) {
      const titleLower = updates.session_title.toLowerCase();
      updateBuilder.addField('session_title', updates.session_title);
      updateBuilder.addField('session_title_lowercase', titleLower);
      updateBuilder.addField('gsi2_sk', `TITLE#${titleLower}`);
      // Determine the generated flag: false if absent or explicitly false, true only if explicitly true.
      const generatedFlag = updates.title_generated === true;
      updateBuilder.addField('title_generated', generatedFlag);
    } else if (updates.title_generated !== undefined) {
      // If ONLY the generated flag is being updated (title is not)...
      updateBuilder.addField('title_generated', updates.title_generated);
    }

    if (updates.language !== undefined) {
      updateBuilder.addField('language', updates.language);
    }

    if (updates.language_locked !== undefined) {
      updateBuilder.addField('language_locked', updates.language_locked);
    }

    if (updates.inspiration_ids !== undefined) {
      if (Array.isArray(updates.inspiration_ids)) {
        updateBuilder.addField('inspiration_ids', updates.inspiration_ids);
      } else {
        this.logger.warn(
          `[${sessionId}] Received non-array value for inspiration_ids during update, skipping.`,
        );
      }
    }

    if (updates.is_bookmarked !== undefined) {
      updateBuilder.addField('is_bookmarked', updates.is_bookmarked);
    }

    if (updates.timezone !== undefined) {
      updateBuilder.addField('timezone', updates.timezone);
    }

    if (updates.schema_version !== undefined) {
      updateBuilder.addField('schema_version', updates.schema_version);
    }

    // Check if we have fields to update
    if (updateBuilder.isEmpty()) {
      this.logger.warn('Update called with no fields to update');
      const current = await this.findById(userId, sessionId);
      if (!current) throw new NotFoundException('Session not found for update');
      return current;
    }

    // Execute update
    try {
      const result = await this.executeUpdate(userId, sessionId, updateBuilder);
      this.logger.log(`Updated session: ${sessionId}`);
      return result;
    } catch (error) {
      // Use error.name check for SDK v3 compatibility
      if (
        error instanceof Error &&
        error.name === 'ConditionalCheckFailedException'
      ) {
        this.logger.warn(`Session not found during update: ${sessionId}`);
        throw new NotFoundException('Session not found for update');
      } else {
        this.logger.error(`Failed to update session ${sessionId}: ${error}`);
        throw new InternalServerErrorException('Could not update session.');
      }
    }
  }

  /**
   * Creates an update builder with common timestamp fields pre-populated
   */
  private createUpdateBuilder(userId: string, timestamp: string) {
    const updateExpressionParts: string[] = [];
    const expressionAttributeValues: Record<string, unknown> = {};
    const expressionAttributeNames: Record<string, string> = {};

    // Add timestamp fields by default
    updateExpressionParts.push('#lastUpdatedAt = :now');
    updateExpressionParts.push('#gsi1sk = :gsi1skValue');
    expressionAttributeValues[':now'] = timestamp;
    expressionAttributeValues[':gsi1skValue'] = `UPDATEDAT#${timestamp}`;
    expressionAttributeNames['#lastUpdatedAt'] = 'last_updated_at';
    expressionAttributeNames['#gsi1sk'] = 'gsi1_sk';

    return {
      addField: (fieldName: string, value: unknown) => {
        const paramName = `:${fieldName.replace(/[^a-zA-Z0-9]/g, '')}`;
        const attrName = `#${fieldName.replace(/[^a-zA-Z0-9]/g, '')}`;
        updateExpressionParts.push(`${attrName} = ${paramName}`);
        expressionAttributeValues[paramName] = value;
        expressionAttributeNames[attrName] = fieldName;
      },
      isEmpty: () => updateExpressionParts.length === 2, // Only has timestamp fields
      getUpdateExpression: () => `SET ${updateExpressionParts.join(', ')}`,
      getExpressionAttributeValues: () => expressionAttributeValues,
      getExpressionAttributeNames: () => expressionAttributeNames,
    };
  }

  /**
   * Executes the DynamoDB update operation
   */
  private async executeUpdate(
    userId: string,
    sessionId: string,
    updateBuilder: ReturnType<typeof this.createUpdateBuilder>,
  ): Promise<Session> {
    const commandInput: UpdateCommandInput = {
      TableName: this.tableName,
      Key: {
        PK: `USER#${userId}`,
        SK: `SESSION#${sessionId}`,
      },
      UpdateExpression: updateBuilder.getUpdateExpression(),
      ExpressionAttributeValues: updateBuilder.getExpressionAttributeValues(),
      ConditionExpression: 'attribute_exists(PK)',
      ReturnValues: 'ALL_NEW',
    };

    const expressionAttributeNames =
      updateBuilder.getExpressionAttributeNames();
    if (Object.keys(expressionAttributeNames).length > 0) {
      commandInput.ExpressionAttributeNames = expressionAttributeNames;
    }

    const command = new UpdateCommand(commandInput);
    const result = await this.ddbDocClient.send(command);
    return result.Attributes as unknown as Session;
  }

  /**
   * Deletes a session item from DynamoDB.
   */
  async delete(userId: string, sessionId: string): Promise<void> {
    const command = new DeleteCommand({
      TableName: this.tableName,
      Key: {
        PK: `USER#${userId}`,
        SK: `SESSION#${sessionId}`,
      },
      ConditionExpression: 'attribute_exists(PK)', // Ensure item exists
    });

    try {
      await this.ddbDocClient.send(command);
      this.logger.log(`Deleted session: ${sessionId}`);
    } catch (error) {
      if (error instanceof ConditionalCheckFailedException) {
        this.logger.warn(
          `Session not found during delete attempt: ${sessionId}`,
        );
        // Deleting something that doesn't exist is often considered idempotent
        return; // Or throw NotFoundException if required
      } else {
        this.logger.error(`Failed to delete session ${sessionId}: ${error}`);
        throw new InternalServerErrorException('Could not delete session.');
      }
    }
  }

  /**
   * Get a session by ID with all metadata
   * @param sessionId Session ID
   * @returns Session metadata or null if not found
   */
  async getSession(sessionId: string): Promise<SessionMetadata | null> {
    try {
      const command = new GetCommand({
        TableName: this.tableName,
        Key: {
          PK: `SESSION#${sessionId}`,
          SK: 'METADATA',
        },
      });

      this.logger.debug(`Retrieving session data for ${sessionId}`);
      const result = await this.ddbDocClient.send(command);

      if (!result.Item) {
        this.logger.warn(`Session ${sessionId} not found`);
        return null;
      }

      // Map DynamoDB item to SessionMetadata with proper type safety
      const item = result.Item as Record<string, unknown>;

      return {
        session_id: item.session_id as string,
        session_title: item.session_title as string,
        language: item.language as SupportedLanguage | null,
        language_locked: Boolean(item.language_locked),
        title_generated: Boolean(item.title_generated),
        created_at: item.created_at as string,
        last_updated_at: item.last_updated_at as string,
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving session ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Update a session's title and optionally mark it as generated
   * @param sessionId Session ID
   * @param title New session title
   * @param titleGenerated Whether the title was auto-generated
   */
  async updateSessionTitle(
    sessionId: string,
    title: string,
    titleGenerated = false,
  ): Promise<void> {
    try {
      const timestamp = new Date().toISOString();

      const command = new UpdateCommand({
        TableName: this.tableName,
        Key: {
          PK: `SESSION#${sessionId}`,
          SK: 'METADATA',
        },
        UpdateExpression:
          'SET session_title = :title, title_generated = :generated, last_updated_at = :timestamp',
        ExpressionAttributeValues: {
          ':title': title,
          ':generated': titleGenerated,
          ':timestamp': timestamp,
        },
        ReturnValues: 'NONE',
      });

      this.logger.debug(
        `Updating session ${sessionId} title to "${title}" (generated=${titleGenerated})`,
      );
      await this.ddbDocClient.send(command);
      this.logger.debug(`Session ${sessionId} title updated successfully`);
    } catch (error) {
      this.logger.error(
        `Error updating session ${sessionId} title: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }
}
