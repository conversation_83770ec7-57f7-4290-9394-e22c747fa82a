import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
  Req,
  OnModuleInit,
  Logger,
} from '@nestjs/common';
import { Request } from 'express';
import { SessionsService } from './sessions.service';
import {
  SessionQueryDto,
  SessionMetadataDto,
  UpdateSessionLanguageDto,
  UpdateSessionTitleDto,
} from './dto';
import { PaginatedResponseDto } from './dto/pagination-response.dto';
import { PaginatedSessionsResponseDto } from './dto/paginated-sessions-response.dto';
import { Auth0Guard } from '../auth/guards/auth0.guard';
import { UserService } from 'src/common/helpers/user-service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { RequireFgaPermission } from 'src/auth/decorators/require-fga-permission.decorator';
import { FGAType, FGARelation } from 'src/auth/fga/fga.enums';
import { getWorkspaceId, setConfigService } from 'src/common/config-helpers';

@ApiTags('Sessions')
@ApiBearerAuth()
@Controller('sessions')
@UseGuards(Auth0Guard)
export class SessionsController implements OnModuleInit {
  private readonly logger = new Logger(SessionsController.name);

  constructor(
    private readonly sessionsService: SessionsService,
    private readonly userService: UserService,
    private readonly configService: ConfigService,
  ) {}

  onModuleInit() {
    setConfigService(this.configService);
  }

  /**
   * POST /sessions
   * Starts a new canvas session with default settings.
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new session' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Session created successfully',
    type: SessionMetadataDto,
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,

    (_req) => getWorkspaceId(),
  )
  async createSession(@Req() request: Request): Promise<SessionMetadataDto> {
    const authorizationHeader = UserService.getAuthorizationHeader(request);
    const user = await this.userService.getInternalUser(authorizationHeader);
    const userId = user.id;
    return this.sessionsService.createSession(userId);
  }

  /**
   * GET /sessions
   * Lists all canvas sessions for the authenticated user, ordered by last updated time (descending).
   * Supports pagination via page number/size and searching by title.
   */
  @Get()
  @ApiOperation({ summary: 'List user sessions' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'page_size',
    required: false,
    type: Number,
    description: 'Items per page (default: 20)',
  })
  @ApiQuery({
    name: 'titleSearch',
    required: false,
    type: String,
    description: 'Filter sessions by title prefix',
  })
  @ApiQuery({
    name: 'bookmarked',
    required: false,
    type: Boolean,
    description: 'Filter sessions by bookmark status',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of sessions retrieved',
    type: PaginatedSessionsResponseDto,
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,

    (_req) => getWorkspaceId(),
  )
  async getSessions(
    @Req() request: Request,
    @Query() queryParams: SessionQueryDto,
  ): Promise<PaginatedResponseDto<SessionMetadataDto>> {
    const authorizationHeader = UserService.getAuthorizationHeader(request);
    const user = await this.userService.getInternalUser(authorizationHeader);
    const userId = user.id;
    const {
      page = 1,
      page_size = 20,
      title_search,
      bookmarked,
      include_untitled,
    } = queryParams;

    return this.sessionsService.getSessions(
      userId,
      page,
      page_size,
      title_search,
      bookmarked,
      include_untitled,
    );
  }

  /**
   * GET /sessions/:sessionId
   * Retrieves metadata for a specific session.
   */
  @Get(':sessionId')
  @ApiOperation({ summary: 'Get session metadata by ID' })
  @ApiParam({
    name: 'sessionId',
    description: 'The ID of the session to retrieve',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Session metadata retrieved',
    type: SessionMetadataDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Session not found',
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,

    (_req) => getWorkspaceId(),
  )
  async getSessionById(
    @Req() request: Request,
    @Param('sessionId') sessionId: string,
  ): Promise<SessionMetadataDto> {
    const authorizationHeader = UserService.getAuthorizationHeader(request);
    const user = await this.userService.getInternalUser(authorizationHeader);
    const userId = user.id;
    return this.sessionsService.getSessionById(userId, sessionId);
  }

  /**
   * PUT /sessions/:sessionId/title
   * Updates the user-editable title of a session.
   */
  @Put(':sessionId/title')
  @ApiOperation({ summary: 'Update session title' })
  @ApiParam({
    name: 'sessionId',
    description: 'The ID of the session to update',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Session title updated',
    type: SessionMetadataDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Session not found',
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,

    (_req) => getWorkspaceId(),
  )
  async updateSessionTitle(
    @Req() request: Request,
    @Param('sessionId') sessionId: string,
    @Body() updateTitleDto: UpdateSessionTitleDto,
  ): Promise<SessionMetadataDto> {
    const authorizationHeader = UserService.getAuthorizationHeader(request);
    const user = await this.userService.getInternalUser(authorizationHeader);
    const userId = user.id;
    return this.sessionsService.updateSessionTitle(
      userId,
      sessionId,
      updateTitleDto.session_title,
    );
  }

  /**
   * PUT /sessions/:sessionId/language
   * Updates the target rule language for a session.
   * Can only be done once before the first chat message is sent (i.e., when language_locked is false).
   */
  @Put(':sessionId/language')
  @ApiOperation({ summary: 'Update session language' })
  @ApiParam({
    name: 'sessionId',
    description: 'The ID of the session to update',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Session language updated',
    type: SessionMetadataDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Session not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Language is locked',
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,

    (_req) => getWorkspaceId(),
  )
  async updateSessionLanguage(
    @Req() request: Request,
    @Param('sessionId') sessionId: string,
    @Body() updateLanguageDto: UpdateSessionLanguageDto,
  ): Promise<SessionMetadataDto> {
    const authorizationHeader = UserService.getAuthorizationHeader(request);
    const user = await this.userService.getInternalUser(authorizationHeader);
    const userId = user.id;
    return this.sessionsService.updateSessionLanguage(
      userId,
      sessionId,
      updateLanguageDto.language,
    );
  }

  /**
   * DELETE /sessions/:sessionId
   * Deletes a canvas session and its metadata from DynamoDB.
   */
  @Delete(':sessionId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a session' })
  @ApiParam({
    name: 'sessionId',
    description: 'The ID of the session to delete',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Session deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Session not found',
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,

    (_req) => getWorkspaceId(),
  )
  async deleteSession(
    @Req() request: Request,
    @Param('sessionId') sessionId: string,
  ): Promise<void> {
    const authorizationHeader = UserService.getAuthorizationHeader(request);
    const user = await this.userService.getInternalUser(authorizationHeader);
    const userId = user.id;
    await this.sessionsService.deleteSession(userId, sessionId);
  }

  /**
   * POST /sessions/:sessionId/bookmark
   * Bookmarks a session for the authenticated user.
   */
  @Post(':sessionId/bookmark')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Bookmark a session' })
  @ApiParam({
    name: 'sessionId',
    description: 'The ID of the session to bookmark',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Session bookmarked successfully',
    type: SessionMetadataDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Session not found',
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (_req) => getWorkspaceId(),
  )
  async bookmarkSession(
    @Req() request: Request,
    @Param('sessionId') sessionId: string,
  ): Promise<SessionMetadataDto> {
    const authorizationHeader = UserService.getAuthorizationHeader(request);
    const user = await this.userService.getInternalUser(authorizationHeader);
    const userId = user.id;
    return this.sessionsService.bookmarkSession(userId, sessionId);
  }

  /**
   * DELETE /sessions/:sessionId/bookmark
   * Unbookmarks a session for the authenticated user.
   */
  @Delete(':sessionId/bookmark')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Unbookmark a session' })
  @ApiParam({
    name: 'sessionId',
    description: 'The ID of the session to unbookmark',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Session unbookmarked successfully',
    type: SessionMetadataDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Session not found',
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (_req) => getWorkspaceId(),
  )
  async unbookmarkSession(
    @Req() request: Request,
    @Param('sessionId') sessionId: string,
  ): Promise<SessionMetadataDto> {
    const authorizationHeader = UserService.getAuthorizationHeader(request);
    const user = await this.userService.getInternalUser(authorizationHeader);
    const userId = user.id;
    return this.sessionsService.unbookmarkSession(userId, sessionId);
  }
}
