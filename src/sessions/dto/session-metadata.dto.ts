import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { SupportedLanguage } from '../../common/languages';

/**
 * DTO for session metadata returned by the API
 * Based on the Session Object (Metadata) from the API contract
 */
export class SessionMetadataDto {
  @ApiProperty({ description: 'Unique identifier for the session' })
  @Expose()
  session_id: string;

  @ApiProperty({ description: 'User-editable title of the session' })
  @Expose()
  session_title: string;

  @ApiProperty({
    description: 'Rule language used for this session',
    enum: SupportedLanguage,
  })
  @Expose()
  language: SupportedLanguage;

  @ApiProperty({
    description: 'Whether the language selection is locked (cannot be changed)',
    default: false,
  })
  @Expose()
  language_locked: boolean;

  @ApiProperty({
    description: 'Whether the title has been set by the user',
    default: false,
  })
  @Expose()
  title_generated: boolean;

  @ApiProperty({
    description: 'ISO 8601 timestamp of when the session was created',
  })
  @Expose()
  created_at: string;

  @ApiProperty({
    description: 'ISO 8601 timestamp of when the session was last updated',
  })
  @Expose()
  last_updated_at: string;

  @ApiProperty({
    description: 'Whether the session is bookmarked by the user',
    default: false,
  })
  @Expose()
  is_bookmarked: boolean;

  @ApiProperty({
    description: 'Array of inspiration IDs used in the session',
    type: [String],
    required: false,
  })
  @Expose()
  inspiration_ids?: string[];

  @ApiProperty({
    description:
      'The timezone to use for the session in the format of an IANA timezone identifier',
    example: 'America/New_York',
  })
  @Expose()
  timezone?: string;
}
