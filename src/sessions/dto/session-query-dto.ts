import { Transform, Type } from 'class-transformer';
import { IsInt, IsOptional, IsString, Min, IsBoolean } from 'class-validator';

/**
 * DTO for pagination query parameters
 * Used with GET /sessions
 */
export class SessionQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page_size?: number = 20;

  @IsOptional()
  @IsString()
  title_search?: string;

  @IsOptional()
  @Type(() => String)
  @Transform(({ value }: { value: any }) => {
    // NestJS really sucks with handling booleans in query params
    // so we need to do this hacky thing to get it to work
    if (value === undefined || value === null) return undefined;
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase();
      return lowerValue === 'true';
    }
    return Boolean(value);
  })
  @IsBoolean()
  bookmarked?: boolean;

  @IsOptional()
  @Type(() => String)
  @Transform(({ value }: { value: any }) => {
    // NestJS really sucks with handling booleans in query params
    // so we need to do this hacky thing to get it to work
    if (value === undefined || value === null) return undefined;
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase();
      return lowerValue === 'true';
    }
    return Boolean(value);
  })
  @IsBoolean()
  include_untitled?: boolean;
}
