import { IsEnum, IsNotEmpty } from 'class-validator';
import { SupportedLanguage } from 'src/common/languages';
/**
 * DTO for updating a session language
 * Used with PUT /sessions/{session_id}/language
 */
export class UpdateSessionLanguageDto {
  @IsEnum(SupportedLanguage, {
    message: `Language must be one of: ${Object.values(SupportedLanguage).join(', ')}`,
  })
  @IsNotEmpty()
  language: SupportedLanguage;
}
