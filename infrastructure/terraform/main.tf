terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# =======================
# Variable Definitions
# =======================
variable "aws_region" {
  description = "The AWS region to deploy resources"
  type        = string
}

variable "aws_account_id" {
  description = "The AWS account ID"
  type        = string
}

variable "environment" {
  description = "Deployment environment (dev, qa, demo, prod)"
  type        = string
}

variable "image_tag" {
  description = "The image tag for the Docker image"
  type        = string
}

variable "cpu" {
  description = "CPU units for the task (1024 = 1 vCPU)"
  type        = number
}

variable "memory" {
  description = "Memory for the task in MB"
  type        = number
}

variable "container_port" {
  description = "Port exposed by the container"
  type        = number
}

variable "secret_path" {
  description = "Path to the secrets in Secrets Manager"
  type        = string
}

# =======================
# Provider Configuration
# =======================
provider "aws" {
  region = var.aws_region
}

# =======================
# Task Definition
# =======================
resource "aws_ecs_task_definition" "task_definition" {
  family                   = "${var.environment}-cti-parsing-middleware-td"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.cpu
  memory                   = var.memory
  execution_role_arn       = "arn:aws:iam::${var.aws_account_id}:role/${var.environment}-s2s-ecs-task-execution-role"
  task_role_arn            = "arn:aws:iam::${var.aws_account_id}:role/${var.environment}-s2s-ecs-task-execution-role"

  container_definitions = jsonencode([
    {
      name      = "cti-parsing-middleware"
      image     = "${var.aws_account_id}.dkr.ecr.${var.aws_region}.amazonaws.com/${var.environment}/s2s-cti-parsing-middleware:${var.image_tag}"
      essential = true
      cpu       = 0
      portMappings = [
        {
          containerPort = var.container_port
          hostPort      = var.container_port
          protocol      = "tcp"
        }
      ]
      secrets = [
        {
          name      = "AWS_REGION"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:AWS_REGION::"
        },
        {
          name      = "AWS_BUCKET_NAME"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:AWS_BUCKET_NAME::"
        },
        {
          name      = "ENV"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:ENV::"
        },
        {
          name      = "AWS_SQS_REQUEST_QUEUE_URL"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:AWS_SQS_REQUEST_QUEUE_URL::"
        },
        {
          name      = "AWS_SQS_RESPONSE_QUEUE_URL"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:AWS_SQS_RESPONSE_QUEUE_URL::"
        },
        {
          name      = "PORT"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:PORT::"
        },
        {
          name      = "RULE_TRANSLATION_BASE_URL"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:RULE_TRANSLATION_BASE_URL::"
        },
        {
          name      = "AUTH0_DOMAIN"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:AUTH0_DOMAIN::"
        },
        {
          name      = "AUTH0_ISSUER_URL"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:AUTH0_ISSUER_URL::"
        },
        {
          name      = "AUTH0_AUDIENCE"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:AUTH0_AUDIENCE::"
        },
        {
          name      = "AUTH0_JWT_CACHE_ENABLED"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:AUTH0_JWT_CACHE_ENABLED::"
        },
        {
          name      = "AUTH0_JWT_REQUESTS_PER_MINUTE"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:AUTH0_JWT_REQUESTS_PER_MINUTE::"
        },
        {
          name      = "AUTH0_JWT_RATE_LIMIT_ENABLED"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:AUTH0_JWT_RATE_LIMIT_ENABLED::"
        },
        {
          name      = "FGA_API_URL"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:FGA_API_URL::"
        },
        {
          name      = "FGA_STORE_ID"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:FGA_STORE_ID::"
        },
        {
          name      = "FGA_API_TOKEN_ISSUER"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:FGA_API_TOKEN_ISSUER::"
        },
        {
          name      = "FGA_API_AUDIENCE"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:FGA_API_AUDIENCE::"
        },
        {
          name      = "FGA_CLIENT_ID"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:FGA_CLIENT_ID::"
        },
        {
          name      = "FGA_CLIENT_SECRET"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:FGA_CLIENT_SECRET::"
        },
        {
          name      = "FGA_WORKSPACE_ID"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:FGA_WORKSPACE_ID::"
        },
        {
          name      = "AUTH0_WEB_CLIENT_ID"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:AUTH0_WEB_CLIENT_ID::"
        },
        {
          name      = "USER_SERVICE_URL"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:USER_SERVICE_URL::"
        },
        {
          name      = "RULE_GENERATOR_WS"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:RULE_GENERATOR_WS::"
        },
        {
          name      = "DYNAMODB_SESSIONS_TABLE"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:DYNAMODB_SESSIONS_TABLE::"
        },
        {
          name      = "SENTRY_DSN_URL"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:SENTRY_DSN_URL::"
        },
        {
          name      = "SENTRY_ENV"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:SENTRY_ENV::"
        },
        {
          name      = "CORS_ALLOWED_ORIGINS"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:CORS_ALLOWED_ORIGINS::"
        },
        {
          name      = "CORS_ALLOWED_METHODS"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:CORS_ALLOWED_METHODS::"
        },
        {
          name      = "CORS_ALLOWED_HEADERS"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:CORS_ALLOWED_HEADERS::"
        },
        {
          name      = "CORS_CREDENTIALS"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:CORS_CREDENTIALS::"
        },
        {
          name      = "CORS_MAX_AGE"
          valueFrom = "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.environment}/${var.secret_path}:CORS_MAX_AGE::"
        }
      ]
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = "/ecs/${var.environment}/cti-parsing-middleware"
          "awslogs-create-group"  = "true"
          "awslogs-region"        = var.aws_region
          "awslogs-stream-prefix" = "ecs"
        }
      }
    }
  ])

  tags = {
    Project     = "s2s"
    Environment = var.environment
    Service     = "cti-parsing-middleware"
    ManagedBy   = "terraform"
    Name        = "${var.environment}-cti-parsing-middleware"
  }
}

# =======================
# Outputs
# =======================
output "ecs_task_definition_arn" {
  description = "The ARN of the ECS Task Definition"
  value       = aws_ecs_task_definition.task_definition.arn
}

output "ecs_task_definition_family" {
  description = "The family of the ECS Task Definition"
  value       = aws_ecs_task_definition.task_definition.family
}
