{"name": "cti-parsing-middleware", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prepare": "husky", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.788.0", "@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/client-sqs": "^3.758.0", "@aws-sdk/lib-dynamodb": "^3.789.0", "@aws-sdk/lib-storage": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@golevelup/ts-jest": "^0.6.2", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^11.0.12", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.2", "@nestjs/platform-socket.io": "^11.0.12", "@nestjs/platform-ws": "^11.0.12", "@nestjs/swagger": "^11.0.6", "@nestjs/websockets": "^11.0.12", "@openfga/sdk": "^0.8.0", "@sentry/nestjs": "^9.15.0", "@types/multer": "^1.4.12", "@types/ws": "^8.18.1", "async-retry": "^1.3.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "luxon": "^3.5.0", "multer": "^2.0.0", "nest-winston": "^1.10.2", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.1", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/joi": "^17.2.2", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": "(src|test)/.*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/src/$1"}}}