# AWS Configuration
AWS_REGION=us-west-2
ENV=dev
AWS_BUCKET_NAME=s2s-bucket-dev
AWS_SQS_REQUEST_QUEUE_URL=https://sqs.us-west-2.amazonaws.com/ACCOUNT_ID/dev-queue-name.fifo
AWS_SQS_RESPONSE_QUEUE_URL=https://sqs.us-west-2.amazonaws.com/ACCOUNT_ID/dev-queue-name.fifo

# Server Configuration
PORT=4005

# Rule Translation
RULE_TRANSLATION_BASE_URL=http://dev-rule-translation-backend:8000

# Auth0 Configuration
AUTH0_DOMAIN=https://your-tenant.auth0.com
AUTH0_ISSUER_URL=https://your-tenant.auth0.com/
AUTH0_AUDIENCE=https://api.example.com
AUTH0_JWT_CACHE_ENABLED=true
AUTH0_JWT_RATE_LIMIT_ENABLED=true
AUTH0_JWT_REQUESTS_PER_MINUTE=5
AUTH0_WEB_CLIENT_ID=your-client-id

# FGA Configuration
FGA_API_URL=https://api.fga.example.com
FGA_STORE_ID=your-store-id
FGA_API_TOKEN_ISSUER=auth.fga.example.com
FGA_API_AUDIENCE=https://api.fga.example.com/
FGA_CLIENT_ID=your-client-id
FGA_CLIENT_SECRET=your-client-secret
FGA_WORKSPACE_ID=your-workspace-id

# Backend Services
USER_SERVICE_URL=https://backend.example.com
RULE_GENERATOR_WS=wss://rule-generator.example.com/api/v1/canvas/websocket

# DCM Configuration
DYNAMODB_SESSIONS_TABLE=canvas_sessions
DYNAMODB_ENDPOINT=http://localhost:8000

# Logging Configuration
APP_LOG_LEVEL=debug

# WebSocket Configuration
WEBSOCKET_HEARTBEAT_INTERVAL_MS=10000 # Interval in milliseconds between heartbeat pings to UI clients

# Backend WebSocket Configuration
BACKEND_HEARTBEAT_INTERVAL_MS=30000 # Interval in milliseconds between heartbeat pings to backend
BACKEND_PONG_TIMEOUT_MS=10000 # Timeout in milliseconds for pong response from backend 

CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*
CORS_CREDENTIALS=false
CORS_MAX_AGE=3600