name: Deploy to ECS

on:
  pull_request:
    types: [closed]
    branches:
      - main
      - qa
      - demo
      - 'release/prod'
  # Add workflow_dispatch trigger for manual deployments
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - qa
          - demo
          - prod

# Add permissions for OIDC
permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    runs-on: GH-Hosted-Linux-x64-Runner
    # Only run this job when a PR is merged or workflow is manually triggered
    if: github.event.pull_request.merged == true || github.event_name == 'workflow_dispatch'

    # Use environment-specific variables
    env:
      AWS_REGION: us-west-2

    steps:
      - name: Checkout Repository with Full History
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Debug GitHub Context
        run: |
          echo "Event name: ${{ github.event_name }}"
          echo "PR merged: ${{ github.event.pull_request.merged || 'N/A' }}"
          echo "Base ref (target branch): ${{ github.event.pull_request.base.ref || 'N/A' }}"
          echo "Head ref (source branch): ${{ github.event.pull_request.head.ref || 'N/A' }}"
          echo "GITHUB_REF: $GITHUB_REF"
          echo "GITHUB_HEAD_REF: $GITHUB_HEAD_REF"
          echo "GITHUB_BASE_REF: $GITHUB_BASE_REF"
          echo "GITHUB_REF_NAME: $GITHUB_REF_NAME"
          echo "Current git branch:"
          git branch --show-current
          echo "Latest commit:"
          git log -1 --pretty=format:"%h %s"
        shell: /usr/bin/bash -e {0}

      - name: Set Environment Variables
        run: |
          # Determine environment based on trigger type
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            # Use the manually selected environment from workflow dispatch
            ENVIRONMENT="${{ github.event.inputs.environment }}"
            echo "Using manually selected environment: $ENVIRONMENT"
          else
            # Get the target branch that the PR was merged into
            TARGET_BRANCH="${{ github.event.pull_request.base.ref }}"
            
            # Set environment based on target branch
            case $TARGET_BRANCH in
              "main")
                ENVIRONMENT="dev"
                ;;
              "qa")
                ENVIRONMENT="qa"
                ;;
              "demo")
                ENVIRONMENT="demo"
                ;;
              "release/prod")
                ENVIRONMENT="prod"
                ;;
              *)
                echo "Invalid target branch: $TARGET_BRANCH"
                exit 1
                ;;
            esac
            echo "Using environment: $ENVIRONMENT based on target branch: $TARGET_BRANCH"
          fi
          
          # Set NEXT_AWS_PUBLIC_ENV based on the environment
          case $ENVIRONMENT in
            "dev")
              NEXT_AWS_PUBLIC_ENV="dev"
              AWS_ACCOUNT_ID="${{ secrets.DEV_AWS_ACCOUNT_ID }}"
              SECRET_NAME="${{ secrets.DEV_SECRET_NAME }}"
              OU="Development"
              ;;
            "qa")
              NEXT_AWS_PUBLIC_ENV="qa"
              AWS_ACCOUNT_ID="${{ secrets.QA_AWS_ACCOUNT_ID }}"
              SECRET_NAME="${{ secrets.QA_SECRET_NAME }}"
              OU="Development"
              ;;
            "demo")
              NEXT_AWS_PUBLIC_ENV="demo"
              AWS_ACCOUNT_ID="${{ secrets.DEMO_AWS_ACCOUNT_ID }}"
              SECRET_NAME="${{ secrets.DEMO_SECRET_NAME }}"
              OU="Demo"
              ;;
            "prod")
              NEXT_AWS_PUBLIC_ENV="production"
              AWS_ACCOUNT_ID="${{ secrets.PROD_AWS_ACCOUNT_ID }}"
              SECRET_NAME="${{ secrets.PROD_SECRET_NAME }}"
              OU="Prod"
              ;;
            *)
              echo "Invalid environment: $ENVIRONMENT"
              exit 1
              ;;
          esac
          
          IMAGE_NAME="s2s-cti-parsing-middleware"
          ECR_REPO="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ENVIRONMENT}/$IMAGE_NAME"
          CLUSTER_NAME="s2s-${ENVIRONMENT}-cluster"
          TASK_DEFINITION_NAME="${ENVIRONMENT}-cti-parsing-middleware-td"
          SERVICE_NAME="${ENVIRONMENT}-cti-parsing-middleware"
          ROLE_NAME="github-deployment-role"

          echo "ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV
          echo "NEXT_AWS_PUBLIC_ENV=$NEXT_AWS_PUBLIC_ENV" >> $GITHUB_ENV
          echo "IMAGE_NAME=$IMAGE_NAME" >> $GITHUB_ENV
          echo "ECR_REPO=$ECR_REPO" >> $GITHUB_ENV
          echo "CLUSTER_NAME=$CLUSTER_NAME" >> $GITHUB_ENV
          echo "TASK_DEFINITION_NAME=$TASK_DEFINITION_NAME" >> $GITHUB_ENV
          echo "SERVICE_NAME=$SERVICE_NAME" >> $GITHUB_ENV
          echo "AWS_ACCOUNT_ID=$AWS_ACCOUNT_ID" >> $GITHUB_ENV
          echo "ROLE_NAME=$ROLE_NAME" >> $GITHUB_ENV
          echo "OU=$OU" >> $GITHUB_ENV
          echo "SECRET_NAME=$SECRET_NAME" >> $GITHUB_ENV
          
          # Print debugging information
          echo "Environment: $ENVIRONMENT"
          echo "NEXT_AWS_PUBLIC_ENV: $NEXT_AWS_PUBLIC_ENV"
          echo "AWS Account ID: $AWS_ACCOUNT_ID"
          echo "OU: $OU"
          echo "ECR Repository URL: $ECR_REPO"
          echo "Secret Name: $SECRET_NAME"
        shell: /usr/bin/bash -e {0}

      - name: Extract Image tag with Branch Name and Commit ID
        run: |
          # Get the commit ID
          COMMIT_ID=$(git log -1 --format=%h)
          
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            # Use the environment name and commit ID for the image tag on manual dispatch
            ENV_NAME="${{ github.event.inputs.environment }}"
            IMAGE_TAG="${ENV_NAME}_${COMMIT_ID}"
          else
            # Use the target branch name that the PR was merged into
            TARGET_BRANCH="${{ github.event.pull_request.base.ref }}"
            
            # Sanitize branch name: convert to lowercase and replace slashes with dashes
            SANITIZED_BRANCH_NAME=$(echo "$TARGET_BRANCH" | sed 's/[A-Z]/\L&/g; s/\//-/g')
    
            # Create the image tag
            IMAGE_TAG="${SANITIZED_BRANCH_NAME}_${COMMIT_ID}"
          fi

          echo "Using IMAGE_TAG: $IMAGE_TAG"
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV

          # Debugging output
          echo "Commit ID: $COMMIT_ID"
        shell: /usr/bin/bash -e {0}

      - name: Configure AWS Credentials with OIDC
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ env.AWS_ACCOUNT_ID }}:role/${{ env.ROLE_NAME }}
          aws-region: ${{ env.AWS_REGION }}
          role-session-name: GithubActionsDeployment
          audience: sts.amazonaws.com
          oidc-provider: token.actions.githubusercontent.com
          oidc-client-id: sts.amazonaws.com

      - name: Set Secret Path
        run: |
          SECRET_PATH="${{ env.SECRET_NAME }}"
          echo "SECRET_PATH=$SECRET_PATH" >> $GITHUB_ENV
        shell: /usr/bin/bash -e {0}

      - name: Login to AWS ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Debug Build Context
        run: ls -la

      - name: Build and Push Multi-Platform Docker Image
        run: |
          docker buildx build \
            --platform linux/amd64 \
            -f ./Dockerfile \
            -t ${{ env.ECR_REPO }}:${{ env.IMAGE_TAG }} \
            --build-arg NEXT_AWS_PUBLIC_ENV=${{ env.NEXT_AWS_PUBLIC_ENV }} \
            --push .

      - name: Set up Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "1.5.7"

      - name: Apply Terraform Changes
        working-directory: infrastructure/terraform
        run: |
          terraform init
          terraform apply -auto-approve \
            -var="aws_region=${AWS_REGION}" \
            -var="aws_account_id=${AWS_ACCOUNT_ID}" \
            -var="environment=${ENVIRONMENT}" \
            -var="image_tag=${IMAGE_TAG}" \
            -var="container_port=4005" \
            -var="cpu=2048" \
            -var="memory=4096" \
            -var="secret_path=${SECRET_PATH}"
        shell: /usr/bin/bash -e {0}
        env:
          AWS_REGION: ${{ env.AWS_REGION }}
          AWS_ACCOUNT_ID: ${{ env.AWS_ACCOUNT_ID }}
          ENVIRONMENT: ${{ env.ENVIRONMENT }}
          IMAGE_TAG: ${{ env.IMAGE_TAG }}
          SECRET_PATH: ${{ env.SECRET_PATH }}

      - name: Update ECS Service
        run: |
          # Retrieve the task definition ARN
          TASK_DEFINITION_ARN=$(aws ecs describe-task-definition \
            --task-definition $TASK_DEFINITION_NAME \
            --query 'taskDefinition.taskDefinitionArn' \
            --output text)

          if [[ -z "$TASK_DEFINITION_ARN" ]]; then
            echo "Error: Failed to retrieve the task definition ARN for $TASK_DEFINITION_NAME."
            exit 1
          fi

          echo "Updating ECS service with task definition: $TASK_DEFINITION_ARN"

          # Update the ECS service with the new task definition
          aws ecs update-service \
            --cluster $CLUSTER_NAME \
            --service $SERVICE_NAME \
            --task-definition $TASK_DEFINITION_ARN \
            --force-new-deployment
        shell: /usr/bin/bash -e {0}
        env:
          TASK_DEFINITION_NAME: ${{ env.TASK_DEFINITION_NAME }}
          CLUSTER_NAME: ${{ env.CLUSTER_NAME }}
          SERVICE_NAME: ${{ env.SERVICE_NAME }}

      - name: Clean Up Workspace
        run: git clean -fdx
        